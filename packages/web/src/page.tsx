import React from 'react'
import { type Metadata } from 'next'
import { Analytics } from '@vercel/analytics/react'

export const metadata: Metadata = {
  title: 'BromieWorks - Brompton Bike Calculator & Garage Management',
  description: 'Optimize your Brompton performance with advanced gear calculations, garage management, and community sharing. The ultimate tool for Brompton owners.',
  keywords: 'Brompton, bike calculator, gear ratios, garage management, cycling',
  openGraph: {
    title: 'BromieWorks - Brompton Bike Calculator & Garage Management',
    description: 'Optimize your Brompton performance with advanced gear calculations, garage management, and community sharing.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BromieWorks - Brompton Bike Calculator & Garage Management',
    description: 'Optimize your Brompton performance with advanced gear calculations, garage management, and community sharing.',
  },
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
}

// Preload critical fonts
const fontPreload = (
  <>
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      as="style"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    />
  </>
)

// Lazy load non-critical sections
const LazyFeaturesSection = React.lazy(() => Promise.resolve({
  default: () => (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need for Your Brompton
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Comprehensive tools designed specifically for Brompton enthusiasts
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Gear Calculator */}
          <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3">Advanced Gear Calculator</h3>
            <p className="text-gray-600 mb-4">
              Calculate optimal gear ratios, speed ranges, and performance metrics for any Brompton configuration
            </p>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Real-time calculations
              </li>
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Multiple gearing options
              </li>
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Performance insights
              </li>
            </ul>
          </div>

          {/* Garage Management */}
          <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3">Smart Garage Management</h3>
            <p className="text-gray-600 mb-4">
              Organize and track multiple Brompton configurations, maintenance schedules, and performance data
            </p>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Multiple bike profiles
              </li>
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Maintenance tracking
              </li>
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Performance history
              </li>
            </ul>
          </div>

          {/* Community Sharing */}
          <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3">Community Sharing</h3>
            <p className="text-gray-600 mb-4">
              Share your configurations, discover setups from other Brompton owners, and get inspired by the community
            </p>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Share configurations
              </li>
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Discover new setups
              </li>
              <li className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Community insights
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  )
}))

// Lazy load email capture section
const LazyEmailSection = React.lazy(() => Promise.resolve({
  default: ({ onSubmit }: { onSubmit: (email: string) => void }) => (
    <section className="py-20 bg-blue-600">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
          Get Early Access
        </h2>
        <p className="text-xl text-blue-100 mb-8">
          Join the waitlist and be the first to experience the future of Brompton optimization
        </p>
        <form 
          className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"
          onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            const email = formData.get('email') as string
            onSubmit(email)
            e.currentTarget.reset()
          }}
        >
          <input
            type="email"
            name="email"
            placeholder="Enter your email"
            className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-300"
            required
            pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
            title="Please enter a valid email address"
          />
          <button
            type="submit"
            className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
          >
            Join Waitlist
          </button>
        </form>
        <p className="text-sm text-blue-200 mt-4">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </section>
  )
}))

// Lazy load testimonials section
const LazyTestimonialsSection = React.lazy(() => Promise.resolve({
  default: () => (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Loved by Brompton Enthusiasts
          </h2>
          <p className="text-xl text-gray-600">
            See what our community members are saying
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gray-300 rounded-full mr-4"></div>
              <div>
                <h4 className="font-semibold">Sarah Chen</h4>
                <p className="text-sm text-gray-600">Brompton C Line Owner</p>
              </div>
            </div>
            <p className="text-gray-600">
              &ldquo;The gear calculator helped me optimize my Brompton for city commuting. I&apos;ve never been happier with my setup!&rdquo;
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gray-300 rounded-full mr-4"></div>
              <div>
                <h4 className="font-semibold">Mike Rodriguez</h4>
                <p className="text-sm text-gray-600">Brompton P Line Owner</p>
              </div>
            </div>
            <p className="text-gray-600">
              &ldquo;Managing multiple Brompton configurations has never been easier. The garage system is a game-changer.&rdquo;
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gray-300 rounded-full mr-4"></div>
              <div>
                <h4 className="font-semibold">Emma Thompson</h4>
                <p className="text-sm text-gray-600">Brompton T Line Owner</p>
              </div>
            </div>
            <p className="text-gray-600">
              &ldquo;I discovered amazing configuration ideas from the community. My Brompton performs better than ever!&rdquo;
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}))

// Lazy load final CTA section
const LazyFinalCTA = React.lazy(() => Promise.resolve({
  default: ({ onClick }: { onClick: () => void }) => (
    <section className="py-20 bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">
          Ready to Optimize Your Brompton?
        </h2>
        <p className="text-xl text-gray-300 mb-8">
          Join thousands of Brompton owners who are already getting the most out of their bikes
        </p>
        <button 
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors transform hover:scale-105 active:scale-95"
          onClick={onClick}
        >
          Get Started Free
        </button>
      </div>
    </section>
  )
}))

function Home() {
  // Analytics event handlers
  const trackEvent = (event: string, data?: Record<string, unknown>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', event, data)
    }
  }

  const handleStartCalculating = () => {
    trackEvent('click', { button_name: 'start_calculating', location: 'hero' })
    // TODO: Navigate to calculator
  }

  const handleLearnMore = () => {
    trackEvent('click', { button_name: 'learn_more', location: 'hero' })
    // TODO: Navigate to features page
  }

  const handleEmailSubmit = (email: string) => {
    trackEvent('submit', { form_name: 'email_capture', email_provided: true })
    // TODO: Implement actual email service integration
    alert(`Thank you! We'll notify you at ${email} when we launch.`)
  }

  const handleGetStarted = () => {
    trackEvent('click', { button_name: 'get_started', location: 'final_cta' })
    // TODO: Navigate to signup
  }

  return (
    <div className="min-h-screen">
      {fontPreload}
      {/* Hero Section - Critical content */}
      <section className="relative bg-gradient-to-br from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Optimize Your Brompton Performance
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              Advanced gear calculator, garage management, and community sharing designed exclusively for Brompton owners
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors transform hover:scale-105 active:scale-95"
                onClick={handleStartCalculating}
              >
                Start Calculating
              </button>
              <button 
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors transform hover:scale-105 active:scale-95"
                onClick={handleLearnMore}
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Lazy loaded sections for better performance */}
      <React.Suspense fallback={<div className="h-20 bg-gray-100 animate-pulse"></div>}>
        <LazyFeaturesSection />
      </React.Suspense>
      
      <React.Suspense fallback={<div className="h-20 bg-blue-100 animate-pulse"></div>}>
        <LazyEmailSection onSubmit={handleEmailSubmit} />
      </React.Suspense>
      
      <React.Suspense fallback={<div className="h-20 bg-gray-100 animate-pulse"></div>}>
        <LazyTestimonialsSection />
      </React.Suspense>
      
      <React.Suspense fallback={<div className="h-20 bg-gray-800 animate-pulse"></div>}>
        <LazyFinalCTA onClick={handleGetStarted} />
      </React.Suspense>
      
      {/* Analytics component */}
      <Analytics />
    </div>
  )
}

export default Home