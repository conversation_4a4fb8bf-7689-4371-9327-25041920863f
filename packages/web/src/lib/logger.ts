import winston from 'winston';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston about the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define formats for web application
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} [WEB] ${info.level}: ${info.message}`,
  ),
);

// Define transports for web application
const transports = [
  // Console transport
  new winston.transports.Console(),
  
  // Error log file
  new winston.transports.File({
    filename: 'logs/web-error.log',
    level: 'error',
  }),
  
  // Combined log file
  new winston.transports.File({ filename: 'logs/web-combined.log' }),
];

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
});

// Export logger functions
export const log = {
  error: (message: string, error?: any) => {
    if (error) {
      logger.error(`${message}: ${error.message || error}`, { stack: error.stack });
    } else {
      logger.error(message);
    }
  },
  warn: (message: string) => {
    logger.warn(message);
  },
  info: (message: string) => {
    logger.info(message);
  },
  http: (message: string) => {
    logger.http(message);
  },
  debug: (message: string) => {
    logger.debug(message);
  },
};

// Helper function to log API requests
export const logApiRequest = (method: string, url: string, statusCode: number, duration?: number, ip?: string) => {
  logger.http(`${method} ${url} - ${statusCode}${duration ? ` (${duration}ms)` : ''}${ip ? ` [${ip}]` : ''}`);
};

// Helper function to log authentication events
export const logAuthEvent = (event: string, details: any = {}) => {
  logger.info(`Auth Event: ${event}`, { details });
};

// Helper function to log rate limiting events
export const logRateLimitEvent = (event: string, ip: string, details: any = {}) => {
  logger.warn(`Rate Limit: ${event} for ${ip}`, { details });
};

// Default export
export default log;