import { NextRequest, NextResponse } from 'next/server';
import { createHash } from 'crypto';

interface RateLimitData {
  count: number;
  resetTime: number;
}

const rateLimits = new Map<string, RateLimitData>();

const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const MAX_REQUESTS = 5; // 5 requests per window

export function createRateLimiter() {
  return function rateLimit(request: NextRequest): NextResponse | null {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const identifier = createHash('sha256').update(ip).digest('hex');
    
    const now = Date.now();
    const windowStart = now - RATE_LIMIT_WINDOW;
    
    // Clean up expired entries
    for (const [key, data] of rateLimits.entries()) {
      if (data.resetTime < now) {
        rateLimits.delete(key);
      }
    }
    
    let data = rateLimits.get(identifier);
    
    if (!data || data.resetTime < now) {
      // Create new window
      data = {
        count: 1,
        resetTime: now + RATE_LIMIT_WINDOW,
      };
      rateLimits.set(identifier, data);
      return null;
    }
    
    if (data.count >= MAX_REQUESTS) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many authentication attempts. Please try again later.',
            retryAfter: Math.ceil((data.resetTime - now) / 1000),
          },
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': MAX_REQUESTS.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': data.resetTime.toString(),
            'Retry-After': Math.ceil((data.resetTime - now) / 1000).toString(),
          },
        }
      );
    }
    
    // Increment counter
    data.count++;
    rateLimits.set(identifier, data);
    
    // Add rate limit headers to successful responses
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', MAX_REQUESTS.toString());
    response.headers.set('X-RateLimit-Remaining', (MAX_REQUESTS - data.count).toString());
    response.headers.set('X-RateLimit-Reset', data.resetTime.toString());
    
    return null;
  };
}

export function applyRateLimit(request: NextRequest): NextResponse | null {
  return createRateLimiter()(request);
}