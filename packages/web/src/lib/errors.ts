export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class RateLimitError extends Error {
  constructor(message: string = 'Too many requests') {
    super(message);
    this.name = 'RateLimitError';
  }
}

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

export function createErrorResponse(
  error: Error,
  statusCode: number = 500
): ErrorResponse {
  return {
    success: false,
    error: {
      code: error.name,
      message: error.message,
      details: (error as any).details,
    },
    timestamp: new Date().toISOString(),
  };
}

export function getErrorStatusCode(error: Error): number {
  switch (error.name) {
    case 'AuthenticationError':
      return 401;
    case 'ValidationError':
      return 400;
    case 'NotFoundError':
      return 404;
    case 'RateLimitError':
      return 429;
    default:
      return 500;
  }
}

export function isAuthenticationError(error: any): error is AuthenticationError {
  return error instanceof AuthenticationError;
}

export function isValidationError(error: any): error is ValidationError {
  return error instanceof ValidationError;
}

export function isNotFoundError(error: any): error is NotFoundError {
  return error instanceof NotFoundError;
}

export function isRateLimitError(error: any): error is RateLimitError {
  return error instanceof RateLimitError;
}