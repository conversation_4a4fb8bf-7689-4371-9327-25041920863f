import React from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  return {
    user: session?.user,
    accessToken: session?.accessToken,
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
    signIn,
    signOut,
  };
}

export function requireAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return React.createElement('div', { 
        className: 'min-h-screen flex items-center justify-center' 
      }, React.createElement('div', { 
        className: 'animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600' 
      }));
    }

    if (!isAuthenticated) {
      return null;
    }

    return React.createElement(WrappedComponent, props);
  };
}