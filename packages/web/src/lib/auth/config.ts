import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { authService } from '@bromieworks/server';

const prisma = new PrismaClient();

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Use the existing auth service for login
          const authResponse = await authService.loginUser(
            credentials.email,
            credentials.password
          );

          return {
            id: authResponse.user.id,
            email: authResponse.user.email,
            name: authResponse.user.name,
            avatar: authResponse.user.avatar,
            access_token: authResponse.accessToken,
            refresh_token: authResponse.refreshToken,
            expires_in: 30 * 60, // 30 minutes
          };
        } catch (error) {
          console.error('Authorization error:', error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 60, // 30 minutes
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    async encode({ secret, token, maxAge }) {
      const encodedToken = jwt.sign(
        {
          ...token,
          exp: Math.floor(Date.now() / 1000) + (maxAge || 30 * 60),
        },
        secret
      );
      return encodedToken;
    },
    async decode({ secret, token }) {
      try {
        const decodedToken = jwt.verify(token as string, secret) as any;
        return decodedToken;
      } catch (error) {
        return null;
      }
    },
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        return {
          ...token,
          accessToken: account.access_token,
          accessTokenExpires: Date.now() + ((account.expires_in as number) || 30 * 60) * 1000,
          refreshToken: account.refresh_token,
          user,
        };
      }

      // Return previous token if the access token has not expired yet
      if (token.accessTokenExpires && Date.now() < token.accessTokenExpires) {
        return token;
      }

      // Access token has expired, try to update it
      if (token.refreshToken) {
        try {
          const refreshedTokens = await authService.refreshToken(token.refreshToken as string);
          
          return {
            ...token,
            accessToken: refreshedTokens.accessToken,
            accessTokenExpires: Date.now() + 30 * 60 * 1000, // 30 minutes
            refreshToken: refreshedTokens.refreshToken ?? token.refreshToken,
          };
        } catch (error) {
          console.error('Failed to refresh access token:', error);
          return {
            ...token,
            error: 'RefreshAccessTokenError',
          };
        }
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.user?.id as string || token.id as string;
        session.user.email = token.user?.email as string || token.email as string;
        session.user.name = token.user?.name as string || token.name as string;
        session.user.avatar = token.user?.avatar as string || token.avatar as string;
        session.accessToken = token.accessToken as string;
        session.error = token.error as string | undefined;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
};