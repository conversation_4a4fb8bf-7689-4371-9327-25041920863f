import 'next-auth'

export {}

declare global {
  interface Window {
    gtag: (
      command: string,
      event: string,
      data?: Record<string, unknown>
    ) => void;
  }
}

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      avatar?: string | null;
    }
    accessToken?: string;
    error?: string;
  }

  interface User {
    id: string;
    email: string;
    name: string;
    avatar?: string | null;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    email: string;
    name: string;
    avatar?: string | null;
    accessToken?: string;
    accessTokenExpires?: number;
    refreshToken?: string;
    user?: {
      id: string;
      email: string;
      name: string;
      avatar?: string | null;
    };
    error?: string;
  }
}