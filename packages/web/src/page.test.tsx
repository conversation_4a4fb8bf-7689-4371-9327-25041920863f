import React from 'react'
import { describe, it, expect } from '@jest/globals'
import { metadata } from './page'
import Home from './page'

// Mock the Next.js metadata
jest.mock('next', () => ({
  ...jest.requireActual('next'),
  metadata: {},
}))

// Simple component test to verify the page exists and can be imported
describe('Landing Page', () => {
  it('should be defined', () => {
    expect(React).toBeDefined()
  })

  it('should have metadata export', () => {
    expect(metadata).toBeDefined()
    expect(metadata.title).toContain('BromieWorks')
  })

  it('should have default export', () => {
    expect(Home).toBeDefined()
    expect(typeof Home).toBe('function')
  })
})