{"name": "@bromieworks/web", "version": "1.0.0", "description": "Next.js web application for BromieWorks", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:unit": "jest", "test:integration": "jest --config=jest.integration.config.js", "test:e2e": "playwright test", "clean": "rm -rf .next dist"}, "dependencies": {"@bromieworks/shared": "workspace:*", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.13.0", "@vercel/analytics": "^1.5.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "^14.0.0", "next-auth": "4.24.0", "prisma": "^6.13.0", "react": "^18.2.0", "react-dom": "^18.2.0", "winston": "^3.17.0", "zod": "^3.22.0"}, "devDependencies": {"@swc/jest": "^0.2.39", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^14.3.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.16", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-mocks-http": "^1.17.2", "playwright": "^1.40.0", "postcss": "^8.4.32", "supertest": "^7.1.4", "tailwindcss": "^3.4.0", "ts-jest": "^29.4.0"}}