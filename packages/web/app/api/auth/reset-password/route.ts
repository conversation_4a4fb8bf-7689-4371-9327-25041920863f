import { NextRequest, NextResponse } from 'next/server';
import { resetPasswordSchema } from '@bromieworks/shared';
import { authService } from '@bromieworks/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const validatedData = resetPasswordSchema.parse(body);
    
    await authService.resetPassword(validatedData.token, validatedData.password);

    return NextResponse.json({
      success: true,
      message: 'Password reset successful'
    });
  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}