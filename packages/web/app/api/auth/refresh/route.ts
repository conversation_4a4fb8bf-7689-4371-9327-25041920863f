import { NextRequest, NextResponse } from 'next/server';
import { refreshTokenSchema } from '@bromieworks/shared';
import { authService } from '@bromieworks/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const validatedData = refreshTokenSchema.parse(body);
    
    const authResponse = await authService.refreshToken(validatedData.refreshToken);

    return NextResponse.json({
      success: true,
      data: {
        user: authResponse.user,
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken
      },
      message: 'Token refreshed successfully'
    });
  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}