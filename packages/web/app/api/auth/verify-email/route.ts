import { NextRequest, NextResponse } from 'next/server';
import { verifyEmailSchema } from '@bromieworks/shared';
import { authService } from '@bromieworks/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const validatedData = verifyEmailSchema.parse(body);
    
    const isVerified = await authService.verifyEmail(validatedData.email, validatedData.code);

    return NextResponse.json({
      success: true,
      data: { isVerified },
      message: 'Email verified successfully'
    });
  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}