import { NextRequest, NextResponse } from 'next/server';
import { registerSchema } from '@bromieworks/shared';
import { authService } from '@bromieworks/server';
import { applyRateLimit } from '@/lib/rate-limit';
import { AuthenticationError, ValidationError, createErrorResponse, getErrorStatusCode } from '@/lib/errors';

export async function POST(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request);
  if (rateLimitResponse) {
    return rateLimitResponse;
  }

  try {
    const body = await request.json();
    
    const validatedData = registerSchema.parse(body);
    
    const user = await authService.registerUser(
      validatedData.email,
      validatedData.password,
      validatedData.name
    );

    return NextResponse.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      },
      message: 'User registered successfully. Please check your email for verification code.',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error instanceof Error) {
      let statusCode = getErrorStatusCode(error);
      
      // Map specific error messages to appropriate status codes
      if (error.message === 'User already exists') {
        statusCode = 409; // Conflict
      }
      
      return NextResponse.json(
        createErrorResponse(error, statusCode),
        { status: statusCode }
      );
    }
    
    return NextResponse.json(
      createErrorResponse(new Error('Internal server error')),
      { status: 500 }
    );
  }
}