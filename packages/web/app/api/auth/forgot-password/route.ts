import { NextRequest, NextResponse } from 'next/server';
import { forgotPasswordSchema } from '@bromieworks/shared';
import { authService } from '@bromieworks/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const validatedData = forgotPasswordSchema.parse(body);
    
    await authService.forgotPassword(validatedData.email);

    return NextResponse.json({
      success: true,
      message: 'Password reset email sent if email exists'
    });
  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}