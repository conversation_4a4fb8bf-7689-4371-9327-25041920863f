import { NextRequest, NextResponse } from 'next/server';
import { loginSchema } from '@bromieworks/shared';
import { authService } from '@bromieworks/server';
import { applyRateLimit } from '@/lib/rate-limit';
import { AuthenticationError, ValidationError, createErrorResponse, getErrorStatusCode } from '@/lib/errors';
import { log, logApiRequest, logAuthEvent, logRateLimitEvent } from '@/lib/logger';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  
  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request);
  if (rateLimitResponse) {
    logRateLimitEvent('rate_limit_exceeded', ip);
    return rateLimitResponse;
  }

  try {
    const body = await request.json();
    
    const validatedData = loginSchema.parse(body);
    
    const authResponse = await authService.loginUser(
      validatedData.email,
      validatedData.password
    );

    const duration = Date.now() - startTime;
    logApiRequest('POST', '/api/auth/login', 200, duration, ip);
    logAuthEvent('login_api_success', { email: validatedData.email, ip, duration });

    return NextResponse.json({
      success: true,
      data: {
        user: authResponse.user,
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken
      },
      message: 'Login successful',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    log.error('Login API error', error);
    logAuthEvent('login_api_failed', { ip, duration, error: error.message });
    
    if (error instanceof Error) {
      let statusCode = getErrorStatusCode(error);
      
      // Map specific error messages to appropriate status codes
      if (error.message === 'Invalid credentials') {
        statusCode = 401;
      } else if (error.message === 'Please verify your email first') {
        statusCode = 403;
      }
      
      logApiRequest('POST', '/api/auth/login', statusCode, duration, ip);
      
      return NextResponse.json(
        createErrorResponse(error, statusCode),
        { status: statusCode }
      );
    }
    
    logApiRequest('POST', '/api/auth/login', 500, duration, ip);
    
    return NextResponse.json(
      createErrorResponse(new Error('Internal server error')),
      { status: 500 }
    );
  }
}