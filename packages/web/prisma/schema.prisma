// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String
  password     String
  avatarUrl    String?
  emailVerified <PERSON>olean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  preferences  Json     @default("{}")
  
  @@map("users")
}

model VerificationCode {
  id        String   @id @default(cuid())
  email     String
  code      String
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  @@map("verification_codes")
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  @@map("password_reset_tokens")
}
