{"name": "@bromieworks/shared", "version": "1.0.0", "description": "Shared types and utilities for BromieWorks", "private": true, "scripts": {"build": "tsc", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:unit": "jest", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "jest": "^29.7.0"}}