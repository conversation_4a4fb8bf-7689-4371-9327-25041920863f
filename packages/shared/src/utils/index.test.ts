import { describe, it, expect } from '@jest/globals'
import {
  formatCurrency,
  formatDate,
  debounce,
  throttle,
  generateId,
  validateEmail,
  sanitizeInput,
  calculatePercentage,
  deepClone
} from './index'

describe('Utility Functions', () => {
  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56')
      expect(formatCurrency(0)).toBe('$0.00')
      expect(formatCurrency(100)).toBe('$100.00')
    })
  })

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const testDate = new Date('2024-01-15')
      expect(formatDate(testDate)).toContain('2024')
      expect(formatDate(testDate)).toContain('January')
      expect(formatDate(testDate)).toContain('15')
    })

    it('should handle string date input', () => {
      expect(formatDate('2024-01-15')).toContain('2024')
    })
  })

  describe('debounce', () => {
    it('should debounce function calls', () => {
      jest.useFakeTimers()
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      expect(mockFn).not.toHaveBeenCalled()
      jest.advanceTimersByTime(100)
      expect(mockFn).toHaveBeenCalledTimes(1)

      jest.useRealTimers()
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', () => {
      jest.useFakeTimers()
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(mockFn).toHaveBeenCalledTimes(1)
      jest.advanceTimersByTime(100)
      throttledFn()
      expect(mockFn).toHaveBeenCalledTimes(2)

      jest.useRealTimers()
    })
  })

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).not.toBe(id2)
      expect(id1).toHaveLength(9)
      expect(id2).toHaveLength(9)
    })
  })

  describe('validateEmail', () => {
    it('should validate email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('')).toBe(false)
    })
  })

  describe('sanitizeInput', () => {
    it('should sanitize input by removing HTML tags', () => {
      expect(sanitizeInput('  hello  ')).toBe('hello')
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script')
      expect(sanitizeInput('normal text')).toBe('normal text')
    })
  })

  describe('calculatePercentage', () => {
    it('should calculate percentage correctly', () => {
      expect(calculatePercentage(25, 100)).toBe(25)
      expect(calculatePercentage(1, 3)).toBe(33)
      expect(calculatePercentage(0, 100)).toBe(0)
      expect(calculatePercentage(10, 0)).toBe(0)
    })
  })

  describe('deepClone', () => {
    it('should create a deep copy of objects', () => {
      const original = { a: 1, b: { c: 2 } }
      const cloned = deepClone(original)
      
      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.b).not.toBe(original.b)
    })
  })
})