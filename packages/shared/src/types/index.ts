// Shared types for BromieWorks application
export * from './auth';
export interface EnvironmentConfig {
  nodeEnv: 'development' | 'production' | 'test';
  databaseUrl: string;
  redisUrl: string;
  jwtSecret: string;
  jwtRefreshSecret: string;
  vercelUrl?: string;
  cloudinaryCloudName?: string;
  cloudinaryApiKey?: string;
  cloudinaryApiSecret?: string;
  nextPublicAppUrl?: string;
  emailHost?: string;
  emailPort?: number;
  emailSecure?: boolean;
  emailUser?: string;
  emailPass?: string;
  emailFrom?: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BromptonModel {
  id: string;
  name: string;
  type: 'C-Line' | 'P-Line' | 'T-Line';
  year: number;
  gearSystem: string;
  wheelSize: number;
  weight: number;
  color: string;
  imageUrl?: string;
  specifications: Record<string, any>;
}

export interface BikeConfiguration {
  id: string;
  userId: string;
  name: string;
  modelId: string;
  components: {
    gears: string;
    wheels: string;
    tires: string;
    saddle: string;
    luggage: string;
    [key: string]: string;
  };
  gearRatios: number[];
  calculatedValues: {
    gearInches: number[];
    development: number[];
    speedRange: {
      min: number;
      max: number;
    };
  };
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
  };
}

// Error types
export class AppError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super('VALIDATION_ERROR', message, details);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super('AUTHENTICATION_ERROR', message);
    this.name = 'AuthenticationError';
  }
}

export class ExternalApiError extends AppError {
  constructor(message: string, details?: any) {
    super('EXTERNAL_API_ERROR', message, details);
    this.name = 'ExternalApiError';
  }
}