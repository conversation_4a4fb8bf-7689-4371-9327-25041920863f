import { z } from 'zod';
import { EnvironmentConfig } from '../types';

const environmentSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Database
  DATABASE_URL: z.string().url('Invalid database URL format'),
  
  // Redis
  REDIS_URL: z.string().url('Invalid Redis URL format'),
  
  // Authentication
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT refresh secret must be at least 32 characters'),
  NEXTAUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters'),
  NEXTAUTH_URL: z.string().url('Invalid NextAuth URL format'),
  
  // Vercel
  VERCEL_URL: z.string().optional(),
  
  // Cloudinary
  CLOUDINARY_CLOUD_NAME: z.string().optional(),
  CLOUDINARY_API_KEY: z.string().optional(),
  CLOUDINARY_API_SECRET: z.string().optional(),
  
  // Email (optional for now)
  EMAIL_FROM: z.string().email().optional(),
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.coerce.number().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  
  // Analytics
  GOOGLE_ANALYTICS_ID: z.string().optional(),
  SENTRY_DSN: z.string().url().optional(),
});

export function validateEnvironment(config: Record<string, unknown>): EnvironmentConfig {
  const result = environmentSchema.safeParse(config);
  
  if (!result.success) {
    const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
    throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
  }
  
  return {
    nodeEnv: result.data.NODE_ENV,
    databaseUrl: result.data.DATABASE_URL,
    redisUrl: result.data.REDIS_URL,
    jwtSecret: result.data.JWT_SECRET,
    jwtRefreshSecret: result.data.JWT_REFRESH_SECRET,
    vercelUrl: result.data.VERCEL_URL,
    cloudinaryCloudName: result.data.CLOUDINARY_API_KEY,
    cloudinaryApiKey: result.data.CLOUDINARY_API_KEY,
    cloudinaryApiSecret: result.data.CLOUDINARY_API_SECRET,
    nextPublicAppUrl: result.data.NEXT_PUBLIC_APP_URL,
    emailHost: result.data.SMTP_HOST,
    emailPort: result.data.SMTP_PORT,
    emailSecure: result.data.EMAIL_SECURE,
    emailUser: result.data.SMTP_USER,
    emailPass: result.data.SMTP_PASS,
    emailFrom: result.data.EMAIL_FROM,
  };
}

export function getEnvironmentConfig(): EnvironmentConfig {
  return validateEnvironment(process.env);
}

// For development/testing
export function createTestConfig(overrides: Partial<EnvironmentConfig> = {}): EnvironmentConfig {
  return validateEnvironment({
    NODE_ENV: 'test',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/bromieworks_test',
    REDIS_URL: 'redis://localhost:6379/1',
    JWT_SECRET: 'test-jwt-secret-min-32-characters-long',
    JWT_REFRESH_SECRET: 'test-jwt-refresh-secret-min-32-characters',
    NEXTAUTH_SECRET: 'test-nextauth-secret-min-32-characters',
    NEXTAUTH_URL: 'http://localhost:3000',
    ...overrides,
  });
}