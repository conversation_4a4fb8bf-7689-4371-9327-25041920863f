import { describe, it, expect } from '@jest/globals';
import { z } from 'zod';
import {
  registerSchema,
  loginSchema,
  refreshTokenSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  verifyEmailSchema,
  RegisterInput,
  LoginInput,
  RefreshTokenInput,
  ForgotPasswordInput,
  ResetPasswordInput,
  VerifyEmailInput
} from '@bromieworks/shared';

describe('Authentication Validation Schemas', () => {
  describe('registerSchema', () => {
    it('should validate correct registration data', () => {
      const validData: RegisterInput = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      const result = registerSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'password123',
        name: 'Test User'
      };

      const result = registerSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject short password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '123',
        name: 'Test User'
      };

      const result = registerSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject short name', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'password123',
        name: ''
      };

      const result = registerSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('loginSchema', () => {
    it('should validate correct login data', () => {
      const validData: LoginInput = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const result = loginSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'password123'
      };

      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: ''
      };

      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('refreshTokenSchema', () => {
    it('should validate correct refresh token', () => {
      const validData: RefreshTokenInput = {
        refreshToken: 'valid-refresh-token'
      };

      const result = refreshTokenSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject empty refresh token', () => {
      const invalidData = {
        refreshToken: ''
      };

      const result = refreshTokenSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('forgotPasswordSchema', () => {
    it('should validate correct email', () => {
      const validData: ForgotPasswordInput = {
        email: '<EMAIL>'
      };

      const result = forgotPasswordSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email'
      };

      const result = forgotPasswordSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('resetPasswordSchema', () => {
    it('should validate correct reset data', () => {
      const validData: ResetPasswordInput = {
        token: 'valid-reset-token',
        password: 'newPassword123'
      };

      const result = resetPasswordSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject empty token', () => {
      const invalidData = {
        token: '',
        password: 'newPassword123'
      };

      const result = resetPasswordSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject short password', () => {
      const invalidData = {
        token: 'valid-reset-token',
        password: '123'
      };

      const result = resetPasswordSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('verifyEmailSchema', () => {
    it('should validate correct verification data', () => {
      const validData: VerifyEmailInput = {
        email: '<EMAIL>',
        code: '123456'
      };

      const result = verifyEmailSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        code: '123456'
      };

      const result = verifyEmailSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject short code', () => {
      const invalidData = {
        email: '<EMAIL>',
        code: '123'
      };

      const result = verifyEmailSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject long code', () => {
      const invalidData = {
        email: '<EMAIL>',
        code: '1234567'
      };

      const result = verifyEmailSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });
});