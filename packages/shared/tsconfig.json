{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": false, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}