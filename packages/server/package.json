{"name": "@bromieworks/server", "version": "1.0.0", "description": "Server-side utilities and services for BromieWorks", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:unit": "jest", "test:integration": "jest --config=jest.integration.config.js", "clean": "rm -rf dist"}, "dependencies": {"@bromieworks/shared": "workspace:*", "@prisma/client": "^6.13.0", "@types/nodemailer": "^6.4.17", "@types/winston": "^2.4.4", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.11.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "tsx": "^4.6.2"}}