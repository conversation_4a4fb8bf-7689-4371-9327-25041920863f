import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AuthService } from './auth.service';
import { PrismaClient } from '@prisma/client';

// Mock Prisma
jest.mock('@prisma/client');
const MockPrismaClient = PrismaClient as jest.MockedClass<typeof PrismaClient>;

describe('AuthService', () => {
  let authService: AuthService;
  let mockPrisma: any;

  beforeEach(() => {
    // Create a new mock PrismaClient instance
    mockPrisma = new MockPrismaClient();
    authService = new AuthService(mockPrisma);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('hashPassword', () => {
    it('should hash password correctly', async () => {
      const password = 'testPassword123';
      const hashedPassword = await authService.hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(0);
    });
  });

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      const password = 'password123';
      const hashedPassword = await authService.hashPassword(password);
      
      const isValid = await authService.verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const password = 'password123';
      const wrongPassword = 'incorrectPassword';
      const hashedPassword = await authService.hashPassword(password);
      
      const isValid = await authService.verifyPassword(wrongPassword, hashedPassword);
      expect(isValid).toBe(false);
    });
  });

  describe('generateAccessToken', () => {
    it('should generate valid JWT access token', async () => {
      const userId = 'test-user-id';
      const token = await authService.generateAccessToken(userId);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate valid JWT refresh token', async () => {
      const userId = 'test-user-id';
      const token = await authService.generateRefreshToken(userId);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
    });
  });

  describe('verifyToken', () => {
    it('should verify valid token', async () => {
      const userId = 'test-user-id';
      const token = await authService.generateAccessToken(userId);
      
      const decoded = await authService.verifyToken(token);
      expect(decoded).toBeDefined();
      expect(decoded.userId).toBe(userId);
      expect(decoded.type).toBe('access');
    });

    it('should reject invalid token', async () => {
      await expect(authService.verifyToken('invalid-token')).rejects.toThrow('Invalid token');
    });
  });

  describe('registerUser', () => {
    it('should register new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockPrisma.user.create.mockResolvedValue({
        id: 'user-id',
        email: userData.email,
        name: userData.name,
        emailVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {}
      });
      mockPrisma.verificationCode.create.mockResolvedValue({});

      const user = await authService.registerUser(userData.email, userData.password, userData.name);
      
      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
    });

    it('should throw error if user already exists', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Existing User'
      };

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'existing-user',
        email: userData.email,
        name: userData.name,
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {}
      });

      await expect(authService.registerUser(userData.email, userData.password, userData.name))
        .rejects.toThrow('User already exists');
    });
  });

  describe('loginUser', () => {
    it('should login user with correct credentials', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      const hashedPassword = await authService.hashPassword('password123');

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user-id',
        email: userData.email,
        name: userData.name,
        password: hashedPassword,
        emailVerified: true,
        avatarUrl: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {}
      });

      const authResponse = await authService.loginUser(userData.email, userData.password);
      
      expect(authResponse).toBeDefined();
      expect(authResponse.user.email).toBe(userData.email);
      expect(authResponse.accessToken).toBeDefined();
      expect(authResponse.refreshToken).toBeDefined();
    });

    it('should throw error for invalid credentials', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const hashedPassword = await authService.hashPassword('password123');

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user-id',
        email: userData.email,
        password: hashedPassword,
        emailVerified: true
      });

      await expect(authService.loginUser(userData.email, userData.password))
        .rejects.toThrow('Invalid credentials');
    });

    it('should throw error if email not verified', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const hashedPassword = await authService.hashPassword('password123');

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user-id',
        email: userData.email,
        password: hashedPassword,
        emailVerified: false
      });

      await expect(authService.loginUser(userData.email, userData.password))
        .rejects.toThrow('Please verify your email first');
    });
  });

  describe('verifyEmail', () => {
    it('should verify email with correct code', async () => {
      const email = '<EMAIL>';
      const code = '123456';

      mockPrisma.verificationCode.findFirst.mockResolvedValue({
        id: 'code-id',
        email,
        code: '123456',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      mockPrisma.user.update.mockResolvedValue({});
      mockPrisma.verificationCode.delete.mockResolvedValue({});

      const result = await authService.verifyEmail(email, code);
      expect(result).toBe(true);
    });

    it('should throw error for invalid code', async () => {
      const email = '<EMAIL>';
      const code = 'wrong-code';

      mockPrisma.verificationCode.findFirst.mockResolvedValue(null);

      await expect(authService.verifyEmail(email, code))
        .rejects.toThrow('Invalid or expired verification code');
    });
  });

  describe('forgotPassword', () => {
    it('should create password reset token for existing user', async () => {
      const email = '<EMAIL>';

      mockPrisma.user.findUnique.mockResolvedValue({ 
        id: 'user-id', 
        email,
        name: 'Test User',
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {}
      });
      mockPrisma.passwordResetToken.create.mockResolvedValue({});

      await expect(authService.forgotPassword(email)).resolves.not.toThrow();
    });

    it('should throw error for non-existent user', async () => {
      const email = '<EMAIL>';

      mockPrisma.user.findUnique.mockResolvedValue(null);

      await expect(authService.forgotPassword(email))
        .rejects.toThrow('User not found');
    });
  });

  describe('resetPassword', () => {
    it('should reset password with valid token', async () => {
      const token = 'valid-token';
      const newPassword = 'newPassword123';

      mockPrisma.passwordResetToken.findFirst.mockResolvedValue({
        id: 'token-id',
        email: '<EMAIL>',
        token: 'valid-token',
        expiresAt: new Date(Date.now() + 60 * 60 * 1000)
      });
      mockPrisma.user.update.mockResolvedValue({});
      mockPrisma.passwordResetToken.delete.mockResolvedValue({});

      await expect(authService.resetPassword(token, newPassword)).resolves.not.toThrow();
    });

    it('should throw error for invalid token', async () => {
      const token = 'invalid-token';
      const newPassword = 'newPassword123';

      mockPrisma.passwordResetToken.findFirst.mockResolvedValue(null);

      await expect(authService.resetPassword(token, newPassword))
        .rejects.toThrow('Invalid or expired reset token');
    });
  });

  describe('refreshToken', () => {
    it('should refresh tokens with valid refresh token', async () => {
      const refreshToken = 'mock-refresh-token';

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: true,
        avatarUrl: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {}
      });

      const authResponse = await authService.refreshToken(refreshToken);
      
      expect(authResponse).toBeDefined();
      expect(authResponse.accessToken).toBeDefined();
      expect(authResponse.refreshToken).toBeDefined();
    });

    it('should throw error for invalid refresh token', async () => {
      await expect(authService.refreshToken('invalid-token'))
        .rejects.toThrow('Invalid refresh token');
    });
  });
});