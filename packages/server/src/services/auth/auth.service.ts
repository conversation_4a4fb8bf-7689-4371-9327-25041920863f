import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import type { User, AuthResponse } from '@bromieworks/shared';
import { getEmailService } from './email.service';
import { log, logAuthEvent, logEmailEvent } from '../../lib/logger';

const prisma = new PrismaClient();

export class AuthService {
  private prisma: PrismaClient;

  constructor(prismaClient?: PrismaClient) {
    this.prisma = prismaClient || prisma;
  }

  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }

  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  async generateAccessToken(userId: string): Promise<string> {
    return jwt.sign(
      { userId, type: 'access' },
      process.env.JWT_SECRET!,
      { expiresIn: '15m' }
    );
  }

  async generateRefreshToken(userId: string): Promise<string> {
    return jwt.sign(
      { userId, type: 'refresh' },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d' }
    );
  }

  async verifyToken(token: string): Promise<{ userId: string; type: string }> {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string; type: string };
      return decoded;
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  async registerUser(email: string, password: string, name: string): Promise<User> {
    const existingUser = await this.prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      throw new Error('User already exists');
    }

    const hashedPassword = await this.hashPassword(password);
    
    const user = await this.prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        preferences: {}
      }
    });

    const verificationCode = this.generateVerificationCode();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await this.prisma.verificationCode.create({
      data: {
        email,
        code: verificationCode,
        expiresAt
      }
    });

    // Send verification email
    try {
      const emailService = getEmailService();
      await emailService.sendVerificationEmail(email, verificationCode);
      logEmailEvent('verification_email_sent', email, { code: verificationCode.substring(0, 3) + '***' });
    } catch (emailError) {
      log.error('Failed to send verification email', emailError);
      // Don't throw here - user is still registered, they can request a new verification code
    }

    return user;
  }

  async loginUser(email: string, password: string): Promise<AuthResponse> {
    logAuthEvent('login_attempt', { email });

    const user = await this.prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      logAuthEvent('login_failed', { email, reason: 'user_not_found' });
      throw new Error('Invalid credentials');
    }

    const isPasswordValid = await this.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      logAuthEvent('login_failed', { email, reason: 'invalid_password' });
      throw new Error('Invalid credentials');
    }

    if (!user.emailVerified) {
      logAuthEvent('login_failed', { email, reason: 'email_not_verified' });
      throw new Error('Please verify your email first');
    }

    const accessToken = await this.generateAccessToken(user.id);
    const refreshToken = await this.generateRefreshToken(user.id);

    logAuthEvent('login_success', { email, userId: user.id });

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatarUrl,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
        preferences: user.preferences as Record<string, unknown>
      },
      accessToken,
      refreshToken
    };
  }

  async verifyEmail(email: string, code: string): Promise<boolean> {
    logAuthEvent('email_verification_attempt', { email, code: code.substring(0, 3) + '***' });

    const verificationCode = await this.prisma.verificationCode.findFirst({
      where: {
        email,
        code,
        expiresAt: { gt: new Date() }
      }
    });

    if (!verificationCode) {
      logAuthEvent('email_verification_failed', { email, reason: 'invalid_or_expired_code' });
      throw new Error('Invalid or expired verification code');
    }

    await this.prisma.user.update({
      where: { email },
      data: { emailVerified: true }
    });

    await this.prisma.verificationCode.delete({
      where: { id: verificationCode.id }
    });

    logAuthEvent('email_verification_success', { email });

    return true;
  }

  async forgotPassword(email: string): Promise<void> {
    logAuthEvent('password_reset_attempt', { email });

    const user = await this.prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      logAuthEvent('password_reset_failed', { email, reason: 'user_not_found' });
      throw new Error('User not found');
    }

    const resetToken = this.generateResetToken();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    await this.prisma.passwordResetToken.create({
      data: {
        email,
        token: resetToken,
        expiresAt
      }
    });

    // Send password reset email
    try {
      const emailService = getEmailService();
      await emailService.sendPasswordResetEmail(email, resetToken);
      logEmailEvent('password_reset_email_sent', email, { tokenPrefix: resetToken.substring(0, 8) + '***' });
    } catch (emailError) {
      log.error('Failed to send password reset email', emailError);
      // Don't throw here - reset token is still created, they can use it manually
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    logAuthEvent('password_reset_attempt', { token: token.substring(0, 8) + '***' });

    const resetToken = await this.prisma.passwordResetToken.findFirst({
      where: {
        token,
        expiresAt: { gt: new Date() }
      }
    });

    if (!resetToken) {
      logAuthEvent('password_reset_failed', { reason: 'invalid_or_expired_token' });
      throw new Error('Invalid or expired reset token');
    }

    const hashedPassword = await this.hashPassword(newPassword);

    await this.prisma.user.update({
      where: { email: resetToken.email },
      data: { password: hashedPassword }
    });

    await this.prisma.passwordResetToken.delete({
      where: { id: resetToken.id }
    });

    logAuthEvent('password_reset_success', { email: resetToken.email });
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as { userId: string; type: string };
      
      if (decoded.type !== 'refresh') {
        logAuthEvent('token_refresh_failed', { reason: 'invalid_token_type' });
        throw new Error('Invalid token type');
      }

      const user = await this.prisma.user.findUnique({
        where: { id: decoded.userId }
      });

      if (!user) {
        logAuthEvent('token_refresh_failed', { userId: decoded.userId, reason: 'user_not_found' });
        throw new Error('User not found');
      }

      const newAccessToken = await this.generateAccessToken(user.id);
      const newRefreshToken = await this.generateRefreshToken(user.id);

      logAuthEvent('token_refresh_success', { userId: user.id, email: user.email });

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatarUrl,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
          preferences: user.preferences as Record<string, unknown>
        },
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      };
    } catch (error) {
      logAuthEvent('token_refresh_failed', { reason: 'invalid_token', error: error.message });
      throw new Error('Invalid refresh token');
    }
  }
}