import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { EmailService, getEmailService, createTestEmailService, resetEmailService } from './email.service';

// Mock nodemailer
jest.mock('nodemailer');
const mockNodemailer = jest.requireMock('nodemailer');

describe('EmailService', () => {
  let emailService: EmailService;
  let mockTransporter: any;

  beforeEach(() => {
    mockTransporter = {
      sendMail: jest.fn().mockResolvedValue({}),
      verify: jest.fn().mockResolvedValue(true),
    };
    
    mockNodemailer.createTransporter = jest.fn().mockReturnValue(mockTransporter);
    
    emailService = new EmailService({
      host: 'smtp.test.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'testpass',
      },
      from: '<EMAIL>',
    });
    
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendEmail', () => {
    it('should send email with correct parameters', async () => {
      const template = {
        subject: 'Test Subject',
        html: '<p>Test HTML</p>',
        text: 'Test Text',
      };

      await emailService.sendEmail('<EMAIL>', template);

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<p>Test HTML</p>',
        text: 'Test Text',
      });
    });

    it('should throw error when email sending fails', async () => {
      mockTransporter.sendMail.mockRejectedValue(new Error('SMTP Error'));

      const template = {
        subject: 'Test Subject',
        html: '<p>Test HTML</p>',
      };

      await expect(emailService.sendEmail('<EMAIL>', template))
        .rejects.toThrow('Failed to send email');
    });
  });

  describe('sendVerificationEmail', () => {
    it('should send verification email with correct template', async () => {
      const code = '123456';
      
      await emailService.sendVerificationEmail('<EMAIL>', code);

      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Verify Your BromieWorks Account',
          html: expect.stringContaining(code),
          text: expect.stringContaining(code),
        })
      );
    });
  });

  describe('sendPasswordResetEmail', () => {
    it('should send password reset email with correct template', async () => {
      const resetToken = 'test-reset-token';
      
      await emailService.sendPasswordResetEmail('<EMAIL>', resetToken);

      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Reset Your BromieWorks Password',
          html: expect.stringContaining(resetToken),
          text: expect.stringContaining(resetToken),
        })
      );
    });
  });

  describe('verifyConnection', () => {
    it('should return true when connection is successful', async () => {
      const result = await emailService.verifyConnection();
      expect(result).toBe(true);
      expect(mockTransporter.verify).toHaveBeenCalled();
    });

    it('should return false when connection fails', async () => {
      mockTransporter.verify.mockRejectedValue(new Error('Connection failed'));
      
      const result = await emailService.verifyConnection();
      expect(result).toBe(false);
    });
  });
});

describe('getEmailService', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
    resetEmailService();
  });

  afterEach(() => {
    process.env = originalEnv;
    resetEmailService();
  });

  it('should create email service with production config', () => {
    process.env.NODE_ENV = 'production';
    process.env.SMTP_HOST = 'smtp.gmail.com';
    process.env.SMTP_PORT = '587';
    process.env.SMTP_USER = '<EMAIL>';
    process.env.SMTP_PASS = 'testpass';
    process.env.EMAIL_FROM = '<EMAIL>';

    mockNodemailer.createTransporter.mockReturnValue({
      sendMail: jest.fn(),
      verify: jest.fn(),
    });

    const service = getEmailService();
    expect(service).toBeInstanceOf(EmailService);
    expect(mockNodemailer.createTransporter).toHaveBeenCalledWith(
      expect.objectContaining({
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'testpass',
        },
      })
    );
  });

  it('should use test email service in development with missing config', () => {
    process.env.NODE_ENV = 'development';
    process.env.SMTP_USER = undefined;
    process.env.SMTP_PASS = undefined;

    const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

    const service = getEmailService();
    expect(service).toBeInstanceOf(EmailService);
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'Email service configuration is incomplete. Email functionality will be limited in development.'
    );

    consoleWarnSpy.mockRestore();
  });

  it('should throw error with incomplete config in production', () => {
    process.env.NODE_ENV = 'production';
    process.env.SMTP_USER = '';
    process.env.SMTP_PASS = '';

    expect(() => getEmailService()).toThrow('Email service configuration is incomplete');
  });
});

describe('createTestEmailService', () => {
  it('should create email service with test configuration', () => {
    mockNodemailer.createTransporter.mockClear();
    
    const testService = createTestEmailService();
    expect(testService).toBeInstanceOf(EmailService);
    expect(mockNodemailer.createTransporter).toHaveBeenCalledWith({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'testpassword',
      },
    });
  });
});