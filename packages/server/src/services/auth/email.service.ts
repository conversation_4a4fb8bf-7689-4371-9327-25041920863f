import nodemailer from 'nodemailer';
import type { Transporter } from 'nodemailer';
import { log, logEmailEvent } from '../../lib/logger';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from?: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export class EmailService {
  private transporter: Transporter;
  private fromAddress: string;

  constructor(config: EmailConfig) {
    this.transporter = nodemailer.createTransporter({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: config.auth,
    });
    this.fromAddress = config.from || config.auth.user;
  }

  async sendEmail(to: string, template: EmailTemplate): Promise<void> {
    try {
      const mailOptions = {
        from: this.fromAddress,
        to,
        subject: template.subject,
        html: template.html,
        text: template.text,
      };

      await this.transporter.sendMail(mailOptions);
      logEmailEvent('email_sent', to, { subject: template.subject });
    } catch (error) {
      log.error('Failed to send email', error);
      throw new Error('Failed to send email');
    }
  }

  async sendVerificationEmail(email: string, code: string): Promise<void> {
    const template: EmailTemplate = {
      subject: 'Verify Your BromieWorks Account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Welcome to BromieWorks!</h2>
          <p>Thank you for registering. Please verify your email address to complete your registration.</p>
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
            <h3 style="color: #1f2937; margin: 0;">Your Verification Code</h3>
            <p style="font-size: 24px; font-weight: bold; color: #2563eb; margin: 10px 0;">${code}</p>
          </div>
          <p>This code will expire in 24 hours.</p>
          <p>If you didn't create this account, please ignore this email.</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">© 2025 BromieWorks. All rights reserved.</p>
        </div>
      `,
      text: `
Welcome to BromieWorks!

Thank you for registering. Please verify your email address to complete your registration.

Your Verification Code: ${code}

This code will expire in 24 hours.

If you didn't create this account, please ignore this email.

© 2025 BromieWorks. All rights reserved.
      `,
    };

    await this.sendEmail(email, template);
  }

  async sendPasswordResetEmail(email: string, resetToken: string): Promise<void> {
    const template: EmailTemplate = {
      subject: 'Reset Your BromieWorks Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Reset Your Password</h2>
          <p>We received a request to reset your password for your BromieWorks account.</p>
          <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #92400e; margin: 0;">Security Notice</h3>
            <p style="color: #92400e; margin: 10px 0;">
              This password reset link will expire in 1 hour for your security.
            </p>
          </div>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
              Reset Password
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px;">
            ${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}
          </p>
          <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">© 2025 BromieWorks. All rights reserved.</p>
        </div>
      `,
      text: `
Reset Your BromieWorks Password

We received a request to reset your password for your BromieWorks account.

Security Notice:
This password reset link will expire in 1 hour for your security.

Reset your password here: ${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}

If you didn't request a password reset, please ignore this email or contact support if you have concerns.

© 2025 BromieWorks. All rights reserved.
      `,
    };

    await this.sendEmail(email, template);
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      log.info('Email service connection verified');
      return true;
    } catch (error) {
      log.error('Email service connection failed', error);
      return false;
    }
  }
}

let emailServiceInstance: EmailService | null = null;

export function getEmailService(): EmailService {
  if (!emailServiceInstance) {
    const emailConfig: EmailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT ? parseInt(process.env.SMTP_PORT) : 587,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
      from: process.env.EMAIL_FROM,
    };

    // In development, allow missing email config with a warning
    if (process.env.NODE_ENV === 'development' && (!emailConfig.auth.user || !emailConfig.auth.pass)) {
      console.warn('Email service configuration is incomplete. Email functionality will be limited in development.');
      return createTestEmailService();
    }

    if (!emailConfig.auth.user || !emailConfig.auth.pass) {
      throw new Error('Email service configuration is incomplete');
    }

    emailServiceInstance = new EmailService(emailConfig);
  }
  return emailServiceInstance;
}

export function createTestEmailService(): EmailService {
  const testConfig: EmailConfig = {
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'testpassword',
    },
    from: '<EMAIL>',
  };
  return new EmailService(testConfig);
}

export function resetEmailService(): void {
  emailServiceInstance = null;
}