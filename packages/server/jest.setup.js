// Jest setup file
import { PrismaClient } from '@prisma/client';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-min-32-characters-long';
process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret-min-32-characters';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/bromieworks_test';

// Mock Prisma
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    verificationCode: {
      findFirst: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    passwordResetToken: {
      findFirst: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  })),
}));

// Mock bcrypt with more flexible mocking
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed-password'),
  compare: jest.fn().mockImplementation((password, hashedPassword) => {
    // Return true only for specific test passwords
    return Promise.resolve(password === 'password123' || password === 'correctpassword');
  }),
}));

// Mock jsonwebtoken with more flexible mocking
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockImplementation((payload, secret, options) => {
    if (payload.type === 'refresh') {
      return 'mock-refresh-token';
    }
    return 'mock-access-token';
  }),
  verify: jest.fn().mockImplementation((token, secret) => {
    if (token === 'invalid-token' || token === 'wrong-code') {
      throw new Error('Invalid token');
    }
    if (token === 'mock-refresh-token') {
      return { userId: 'test-user-id', type: 'refresh' };
    }
    return { userId: 'test-user-id', type: 'access' };
  }),
}));

// Mock crypto
jest.mock('crypto', () => ({
  randomBytes: jest.fn().mockReturnValue({
    toString: jest.fn().mockReturnValue('mock-token'),
  }),
  createHash: jest.fn().mockReturnValue({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn().mockReturnValue('mock-hash'),
  }),
}));

// Setup test timeout
jest.setTimeout(30000);

// Global test teardown
afterAll(() => {
  // Clean up any resources if needed
});