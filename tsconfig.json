{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@bromieworks/web": ["./packages/web/src"], "@bromieworks/web/*": ["./packages/web/src/*"], "@bromieworks/server": ["./packages/server/src"], "@bromieworks/server/*": ["./packages/server/src/*"], "@bromieworks/shared": ["./packages/shared/src"], "@bromieworks/shared/*": ["./packages/shared/src/*"]}}, "include": [], "references": [{"path": "./packages/web"}, {"path": "./packages/server"}, {"path": "./packages/shared"}], "exclude": ["node_modules", "dist", ".next"]}