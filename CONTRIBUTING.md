# Contributing to BromieWorks

We love your input! We want to make contributing to BromieWorks as easy and transparent as possible, whether it's:

- Reporting a bug
- Discussing the current state of the code
- Submitting a fix
- Proposing new features
- Becoming a maintainer

## Development Process

We use a simple Git flow:

- `main` - Production-ready code
- `develop` - Development branch
- `feature/*` - Feature branches
- `hotfix/*` - Critical fixes

## Getting Started

### Prerequisites

- Node.js >= 20.11.0
- PNPM >= 8.0.0
- PostgreSQL 15.0+
- Redis 7.2+

### Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/bromieworks.git
   cd bromieworks
   ```

2. **Add upstream remote**
   ```bash
   git remote add upstream https://github.com/bromieworks/bromieworks.git
   ```

3. **Install dependencies**
   ```bash
   pnpm install:all
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your values
   ```

5. **Set up database**
   ```bash
   createdb bromieworks_dev
   pnpm db:migrate
   ```

### Development Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name develop
   ```

2. **Make your changes**
   - Follow the code style guidelines
   - Write tests for new functionality
   - Ensure all tests pass
   - Update documentation as needed

3. **Run quality checks**
   ```bash
   # Run all checks
   pnpm lint && pnpm typecheck && pnpm test
   
   # Or run individually
   pnpm lint
   pnpm typecheck
   pnpm test:unit
   pnpm test:integration
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```
   Then create a Pull Request to the `develop` branch.

## Code Style Guidelines

### TypeScript

- **Strict Mode**: Always enabled
- **Type Safety**: Use explicit types, avoid `any`
- **Interfaces**: Use for object shapes
- **Types**: Use for unions, primitives, utility types

```typescript
// Good
interface User {
  id: string;
  name: string;
  email: string;
}

const getUser = (id: string): Promise<User> => {
  // implementation
};

// Bad
const getUser = (id) => {
  // implementation
};
```

### React Components

- **Functional Components**: Use functional components with hooks
- **TypeScript**: Use TypeScript interfaces for props
- **Naming**: PascalCase for components, camelCase for handlers
- **Hooks**: Follow React hooks rules

```typescript
// Good
interface BikeCardProps {
  bike: BikeConfiguration;
  onSelect?: (bike: BikeConfiguration) => void;
}

const BikeCard: React.FC<BikeCardProps> = ({ bike, onSelect }) => {
  const [isSelected, setIsSelected] = useState(false);
  
  return (
    <div className={`bike-card ${isSelected ? 'selected' : ''}`}>
      {/* component content */}
    </div>
  );
};

// Bad
function BikeCard(props) {
  // implementation
}
```

### Testing

- **Unit Tests**: Co-located with source files (`*.test.ts`)
- **Test Structure**: Arrange, Act, Assert pattern
- **Mocking**: Mock external dependencies
- **Coverage**: Minimum 90% for critical logic

```typescript
// Good
describe('GearCalculator', () => {
  describe('calculateGearRatios', () => {
    it('should calculate correct gear ratios for given configuration', () => {
      // Arrange
      const calculator = new GearCalculator();
      const config = createTestConfig();
      
      // Act
      const result = calculator.calculateGearRatios(config);
      
      // Assert
      expect(result).toEqual(expectedRatios);
    });
  });
});
```

## Pull Request Process

1. **PR Description**
   - Clear title following [Conventional Commits](https://www.conventionalcommits.org/)
   - Detailed description of changes
   - Related issue number (e.g., "Fixes #123")
   - Testing performed
   - Screenshots if UI changes

2. **PR Requirements**
   - All tests pass
   - Code follows style guidelines
   - Documentation updated
   - No breaking changes (or clearly documented)
   - Performance impact considered

3. **Review Process**
   - At least one maintainer approval required
   - Automated checks must pass
   - Address review comments promptly
   - Keep PRs focused and small

## Commit Message Format

We use [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `build`: Changes that affect the build system or external dependencies
- `ci`: Changes to our CI configuration files and scripts
- `chore`: Other changes that don't modify src or test files

### Examples

```bash
feat(gear-calculator): add gear ratio optimization
fix(auth): resolve login issue on mobile devices
docs(readme): update installation instructions
style(button): improve button styling
refactor(utils): simplify calculation logic
perf(api): optimize database queries
test(auth): add login test cases
build: upgrade to Next.js 14
ci: add GitHub Actions workflow
```

## Reporting Issues

### Bug Reports

When creating a bug report, please include:

1. **Environment**
   - OS and version
   - Browser and version
   - Node.js version

2. **Steps to Reproduce**
   - Clear, reproducible steps
   - Expected behavior
   - Actual behavior

3. **Additional Context**
   - Screenshots
   - Error messages
   - Console logs

### Feature Requests

For feature requests, please include:

1. **Problem Statement**
   - What problem are you trying to solve?
   - Current workarounds

2. **Proposed Solution**
   - Detailed description of the feature
   - User benefits

3. **Alternatives**
   - Other solutions you've considered
   - Why your proposal is better

## Code of Conduct

This project follows the [Contributor Covenant](CODE_OF_CONDUCT.md). Please read and follow our code of conduct.

## Getting Help

- **Documentation**: Check our [docs/](docs/) folder
- **Issues**: Search existing issues or create a new one
- **Discussions**: Join our GitHub Discussions
- **Discord**: Join our community server

## Becoming a Maintainer

We welcome new maintainers! To become a maintainer:

1. **Active Contribution**: Regular, high-quality contributions
2. **Code Reviews**: Participate in reviewing PRs
3. **Issue Triage**: Help manage and triage issues
4. **Community**: Help and support other contributors

Current maintainers will review your contributions and invite you to join the team.

---

Thank you for contributing to BromieWorks! 🚀