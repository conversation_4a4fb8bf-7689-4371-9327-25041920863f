# BromieWorks

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D20.11.0-brightgreen)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3.3-blue)](https://www.typescriptlang.org/)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/bromieworks/bromieworks/pulls)

A comprehensive Brompton bike calculator and garage management application that helps cyclists optimize their gear ratios, manage their bike configurations, and share their setups with the community.

## 🚀 Features

- **Gear Calculator**: Calculate optimal gear ratios for Brompton bikes
- **Garage Management**: Organize and track multiple bike configurations
- **Social Sharing**: Share your bike setups with the community
- **Mobile-First Design**: Responsive interface optimized for all devices
- **Real-time Calculations**: Instant feedback on gear ratio changes
- **Community Features**: Browse and discover configurations from other users

## 🛠️ Technology Stack

- **Frontend**: Next.js 14.0.0, React 18.2.0, TypeScript 5.3.3
- **Backend**: Next.js API Routes, PostgreSQL 15.0, Prisma ORM
- **Authentication**: NextAuth.js 4.24.0
- **Caching**: Redis 7.2
- **Deployment**: Vercel, Supabase
- **Testing**: Jest 29.7.0, Playwright 1.40.0
- **Package Manager**: PNPM 8.15.0

## 📦 Project Structure

```
bromieworks/
├── packages/
│   ├── web/          # Next.js frontend application
│   ├── server/       # Server-side utilities and services
│   └── shared/       # Shared types and utilities
├── scripts/          # Development and deployment scripts
├── tests/           # Test files (unit, integration, e2e)
├── docs/            # Project documentation
└── .github/         # GitHub Actions workflows
```

## 🚀 Quick Start

### Prerequisites

- Node.js >= 20.11.0
- PNPM >= 8.0.0
- PostgreSQL 15.0+
- Redis 7.2+

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/bromieworks/bromieworks.git
   cd bromieworks
   ```

2. **Install dependencies**
   ```bash
   pnpm install:all
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   Edit `.env.local` with your configuration values.

4. **Set up the database**
   ```bash
   # Create database
   createdb bromieworks
   
   # Run migrations
   pnpm db:migrate
   
   # Seed database (optional)
   pnpm db:seed
   ```

5. **Start Redis**
   ```bash
   redis-server
   ```

6. **Run the development server**
   ```bash
   pnpm dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📝 Development

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm test` - Run all tests
- `pnpm test:unit` - Run unit tests only
- `pnpm test:integration` - Run integration tests only
- `pnpm test:e2e` - Run end-to-end tests
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint issues
- `pnpm typecheck` - Run TypeScript type checking
- `pnpm format` - Format code with Prettier
- `pnpm format:check` - Check code formatting

### Code Style

- **TypeScript**: Strict mode enabled, comprehensive type definitions
- **ESLint**: Code linting with TypeScript and React rules
- **Prettier**: Code formatting with consistent style
- **Husky**: Pre-commit hooks for code quality
- **Naming Conventions**:
  - Components: PascalCase (`BikeCard`)
  - Variables: camelCase (`gearRatio`)
  - Constants: SCREAMING_SNAKE_CASE (`MAX_RETRIES`)
  - Files: kebab-case (`bike-config.component.tsx`)

### Testing

- **Unit Tests**: Jest with 90% coverage requirement
- **Integration Tests**: Jest with Testcontainers
- **E2E Tests**: Playwright for critical user journeys
- **Test Organization**: Co-located with source files (`*.test.ts`)

## 🚀 Deployment

### Vercel

1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables

Required variables for production:
```bash
NODE_ENV=production
DATABASE_URL=your-production-db-url
REDIS_URL=your-production-redis-url
JWT_SECRET=your-production-jwt-secret
NEXTAUTH_SECRET=your-production-nextauth-secret
NEXTAUTH_URL=https://bromieworks.vercel.app
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and ensure code quality (`pnpm test && pnpm lint`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Brompton Bicycle](https://www.brompton.com/) for inspiring this project
- [Next.js](https://nextjs.org/) for the amazing framework
- [Vercel](https://vercel.com/) for the hosting platform
- The open-source community for the tools and libraries used

## 📞 Support

If you have any questions or need help, please:

1. Check our [Documentation](./docs/)
2. Search existing [Issues](https://github.com/bromieworks/bromieworks/issues)
3. Create a new issue if needed

---

Built with ❤️ by the BromieWorks team