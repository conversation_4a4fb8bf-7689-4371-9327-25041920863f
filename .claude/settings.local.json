{"permissions": {"allow": ["Bash(md-tree explode:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(pnpm install:*)", "Bash(pnpm test:*)", "Bash(npm run test)", "Bash(npm install:*)", "Bash(pnpm add:*)", "Bash(pnpm lint:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx eslint:*)", "Bash(pnpm run build)", "Bash(pnpm --filter @bromieworks/web add @typescript-eslint/eslint-plugin@^6.21.0 @typescript-eslint/parser@^6.21.0 --save-dev)", "Bash(pnpm run lint:*)", "Bash(pnpm --filter @bromieworks/shared add @typescript-eslint/eslint-plugin@^6.21.0 @typescript-eslint/parser@^6.21.0 --save-dev)", "Bash(pnpm ls:*)", "Bash(pnpm --filter @bromieworks/web run lint:*)", "Bash(npx next lint:*)", "Bash(pnpm --filter @bromieworks/web add next-auth@4.24.0 @next-auth/prisma-adapter)", "Bash(pnpm --filter @bromieworks/web add:*)", "Bash(npx prisma init:*)", "Bash(npm run typecheck:*)", "Bash(pnpm typecheck:*)", "Bash(npm test)", "Bash(npm run lint)", "Bash(npm run build:*)", "Bash(cp:*)", "Bash(pnpm -r build)", "Bash(pnpm --filter server test)", "Bash(pnpm --filter web test)", "Bash(pnpm --filter server add nodemailer @types/nodemailer)", "Bash(pnpm --filter web add -D supertest @types/supertest)", "Bash(pnpm --filter web add -D node-mocks-http @types/node-mocks-http)", "Bash(pnpm --filter web add -D node-mocks-http)", "Bash(pnpm --filter server add winston @types/winston)", "Bash(pnpm --filter web add winston)"], "deny": []}}