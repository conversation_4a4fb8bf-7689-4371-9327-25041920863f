# Components

## API Gateway
**Responsibility:** Central entry point for all API requests with authentication, rate limiting, and routing

**Key Interfaces:**
- RESTful API endpoints following OpenAPI 3.0 specification
- JWT token validation and refresh
- Request/response logging and monitoring
- CORS configuration for web frontend

**Dependencies:** Vercel platform, Redis cache, PostgreSQL database
**Technology Stack:** Next.js middleware, NextAuth.js, custom rate limiting middleware

## Authentication Service
**Responsibility:** User registration, login, and session management

**Key Interfaces:**
- /api/auth/register - Email-based user registration
- /api/auth/login - Secure user authentication
- /api/auth/refresh - JWT token refresh
- /api/auth/logout - Session termination

**Dependencies:** Email service provider, User data model, Redis session storage
**Technology Stack:** NextAuth.js, JWT tokens, email service integration

## Gear Calculation Engine
**Responsibility:** Core business logic for gear ratio and performance calculations

**Key Interfaces:**
- calculateGearRatio(configuration) - Calculate gear ratio for given configuration
- calculateDevelopment(configuration) - Calculate wheel development
- calculateSpeedRange(configuration, cadences) - Calculate speed at different cadences
- validateCompatibility(components) - Validate component compatibility

**Dependencies:** Component specifications, mathematical formulas, Redis cache
**Technology Stack:** TypeScript calculation modules, Redis for caching results

## User Management Service
**Responsibility:** User profile and garage management

**Key Interfaces:**
- /api/users/profile - User profile CRUD operations
- /api/garage - Garage bike management
- /api/configurations - Configuration management
- /api/users/preferences - User preference management

**Dependencies:** User data model, Garage data model, Configuration data model
**Technology Stack:** Next.js API routes, Prisma ORM, validation schemas

## Brompton Data Service
**Responsibility:** Brompton model and component data management

**Key Interfaces:**
- /api/models - Brompton model browsing and search
- /api/components - Component specifications and compatibility
- /api/models/{id} - Detailed model information
- /api/components/compatible - Find compatible components

**Dependencies:** BromptonModel data model, Component data model, Redis cache
**Technology Stack:** Next.js API routes, Prisma ORM, search optimization

## Social Sharing Service
**Responsibility:** Social media image generation and sharing functionality

**Key Interfaces:**
- /api/share/generate - Generate shareable images
- /api/share/upload - Upload to social platforms
- /api/share/community - Community browsing
- /api/share/{code} - Access shared configurations

**Dependencies:** Cloudinary image service, social media APIs, SharedConfiguration model
**Technology Stack:** Image generation library, social media SDKs, CDN delivery

## Component Diagrams
```mermaid
graph TB
    subgraph "Frontend"
        F[React Components]
    end
    
    subgraph "API Layer"
        AG[API Gateway]
        AUTH[Authentication Service]
        UM[User Management]
        GE[Gear Calculation Engine]
        BD[Brompton Data Service]
        SS[Social Sharing Service]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        RD[(Redis Cache)]
        CS[Cloudinary Storage]
    end
    
    subgraph "External"
        SM[Social Media APIs]
        ES[Email Service]
    end
    
    F --> AG
    AG --> AUTH
    AG --> UM
    AG --> GE
    AG --> BD
    AG --> SS
    
    AUTH --> PG
    AUTH --> RD
    UM --> PG
    UM --> RD
    GE --> RD
    BD --> PG
    BD --> RD
    SS --> PG
    SS --> CS
    SS --> SM
    
    AUTH --> ES
```