# Error Handling Strategy

## General Approach
- **Error Model:** Structured error responses with error codes, messages, and contextual data
- **Exception Hierarchy:** Custom exception classes for different error types (ValidationError, AuthenticationError, BusinessLogicError, ExternalApiError)
- **Error Propagation:** Consistent error handling middleware with appropriate HTTP status codes and user-friendly messages

## Logging Standards
- **Library:** Winston 3.11.0
- **Format:** JSON structured logging with correlation IDs
- **Levels:** error, warn, info, debug with appropriate usage guidelines
- **Required Context:**
  - Correlation ID: UUID generated per request
  - Service Context: Service name and version
  - User Context: User ID (when available), request IP

## Error Handling Patterns

### External API Errors
- **Retry Policy:** Exponential backoff with jitter, max 3 retries
- **Circuit Breaker:** Open circuit after 5 consecutive failures, 30-second timeout
- **Timeout Configuration:** 10-second timeout for external API calls
- **Error Translation:** Map external API errors to internal error codes with user-friendly messages

### Business Logic Errors
- **Custom Exceptions:** ValidationError, ConfigurationError, CalculationError
- **User-Facing Errors:** Clear, actionable error messages with suggested solutions
- **Error Codes:** Standardized error codes for frontend handling (e.g., 'INVALID_CONFIGURATION', 'COMPONENT_INCOMPATIBLE')

### Data Consistency
- **Transaction Strategy:** Database transactions for multi-table operations
- **Compensation Logic:** Rollback mechanisms for failed operations
- **Idempotency:** Idempotent API endpoints with request deduplication