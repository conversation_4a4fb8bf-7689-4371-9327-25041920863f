# Data Models

## User
**Purpose:** Represents application users with authentication and profile information

**Key Attributes:**
- id: UUID - Primary identifier
- email: String - Unique email address for authentication
- name: String - User's display name
- avatar: String? - Profile image URL
- createdAt: DateTime - Account creation timestamp
- updatedAt: DateTime - Last profile update timestamp
- preferences: JSON - User settings and preferences

**Relationships:**
- One-to-many with Garage bikes
- One-to-many with Configurations
- One-to-many with Shared configurations

## BromptonModel
**Purpose:** Official Brompton bicycle models with specifications

**Key Attributes:**
- id: UUID - Primary identifier
- name: String - Model name (e.g., "C Line Explore")
- line: String - Brompton line (A, C, P, T, G)
- type: String - Model type (Urban, Explore, Utility, etc.)
- basePrice: Decimal - Manufacturer suggested retail price
- weight: Float - Bike weight in kg
- createdAt: DateTime - Record creation timestamp
- updatedAt: DateTime - Last update timestamp

**Relationships:**
- One-to-many with ModelComponents
- One-to-many with Garage bikes (as base model)

## Component
**Purpose:** Individual bike components (chainrings, sprockets, tires, hubs)

**Key Attributes:**
- id: UUID - Primary identifier
- type: String - Component type (chainring, sprocket, tire, hub)
- name: String - Component name
- specification: JSON - Technical specifications (teeth count, size, etc.)
- compatibleLines: String[] - Compatible Brompton lines
- createdAt: DateTime - Record creation timestamp

**Relationships:**
- Many-to-many with BromptonModel through ModelComponents

## Garage
**Purpose:** User's personal bike collection

**Key Attributes:**
- id: UUID - Primary identifier
- userId: UUID - Foreign key to User
- nickname: String - User-provided bike name
- bromptonModelId: UUID - Base Brompton model
- isPrimary: Boolean - Whether this is the user's primary bike
- purchaseDate: Date? - When user acquired the bike
- notes: String? - User notes about the bike
- createdAt: DateTime - Garage entry creation timestamp
- updatedAt: DateTime - Last update timestamp

**Relationships:**
- Many-to-one with User
- Many-to-one with BromptonModel
- One-to-many with Configurations

## Configuration
**Purpose:** Custom bike configurations with performance data

**Key Attributes:**
- id: UUID - Primary identifier
- garageId: UUID - Foreign key to Garage
- name: String - Configuration name
- chainringId: UUID - Selected chainring component
- sprocketId: UUID - Selected sprocket component
- hubId: UUID - Selected hub component
- tireId: UUID - Selected tire component
- gearRatio: Float - Calculated gear ratio
- development: Float - Calculated development in meters
- speedRange: JSON - Calculated speed range at different cadences
- isActive: Boolean - Whether this is the active configuration
- createdAt: DateTime - Configuration creation timestamp
- updatedAt: DateTime - Last update timestamp

**Relationships:**
- Many-to-one with Garage
- Many-to-one with Component (chainring, sprocket, hub, tire)

## SharedConfiguration
**Purpose:** Publicly shared configurations for community features

**Key Attributes:**
- id: UUID - Primary identifier
- configurationId: UUID - Foreign key to Configuration
- userId: UUID - User who shared it
- shareCode: String - Unique share code
- title: String - Share title
- description: String? - Share description
- isPublic: Boolean - Whether publicly visible
- views: Integer - View count
- likes: Integer - Like count
- createdAt: DateTime - Share creation timestamp

**Relationships:**
- Many-to-one with Configuration
- Many-to-one with User