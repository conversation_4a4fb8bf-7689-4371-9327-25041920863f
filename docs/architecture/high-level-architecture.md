# High Level Architecture

## Technical Summary
BromieWorks employs a serverless architecture using Next.js API routes within a monorepo structure, providing scalable compute without operational overhead. The system uses PostgreSQL for persistent data storage with Redis caching for performance optimization, implementing a gear calculation engine that powers real-time bicycle performance analytics. This architecture supports the PRD's goals for mobile-first user experience, social sharing capabilities, and accurate gear ratio calculations while maintaining cost-effectiveness for a small team deployment.

## High Level Overview
**Main Architectural Style:** Serverless functions within monolithic structure using Next.js API routes
**Repository Structure:** Monorepo with separate packages for frontend, backend, and shared utilities
**Service Architecture:** Serverless with integrated database and caching layers
**Primary User Flow:** Mobile web user → Next.js frontend → API routes → PostgreSQL/Redis → Social media sharing
**Key Architectural Decisions:**
- Serverless for cost optimization and automatic scaling
- Monorepo for simplified dependency management
- PostgreSQL for relational data integrity
- Redis for high-performance caching of calculations
- JWT authentication for stateless scalability

## High Level Project Diagram
```mermaid
graph TB
    subgraph "User Layer"
        U[Mobile Web User]
        P[PWA Installation]
    end
    
    subgraph "Frontend Layer"
        F[Next.js App Router]
        C[React Components]
        S[Service Worker]
    end
    
    subgraph "API Layer"
        A[Next.js API Routes]
        J[JWT Middleware]
        R[Rate Limiting]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        RD[(Redis Cache)]
        CS[Cloud Storage]
    end
    
    subgraph "External Services"
        SM[Social Media APIs]
        EM[Email Service]
        IMG[Image Generation]
    end
    
    U --> F
    P --> F
    F --> A
    A --> J
    A --> R
    A --> PG
    A --> RD
    A --> CS
    A --> SM
    A --> EM
    A --> IMG
```

## Architectural and Design Patterns
- **Serverless Architecture:** Using Vercel/Next.js API routes for compute - _Rationale:_ Aligns with PRD requirement for cost optimization and automatic scaling without operational overhead
- **Repository Pattern:** Abstracting data access logic with Prisma ORM - _Rationale:_ Enables testing consistency and future database migration flexibility
- **CQRS Pattern:** Separating read and write operations for performance - _Rationale:_ Optimizes for high read volumes of Brompton specifications and user garage data
- **Event-Driven Communication:** Using Redis pub/sub for real-time updates - _Rationale:_ Supports live configuration updates and collaborative features
- **JWT Authentication:** Stateless authentication with refresh tokens - _Rationale:_ Scales efficiently in serverless environment