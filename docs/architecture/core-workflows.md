# Core Workflows

## User Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant AG as API Gateway
    participant AUTH as Auth Service
    participant DB as Database
    participant EM as Email Service
    
    U->>F: Enter email/password
    F->>AG: POST /api/auth/login
    AG->>AUTH: Validate credentials
    AUTH->>DB: Query user record
    DB-->>AUTH: User data
    AUTH->>AG: Generate JWT tokens
    AG-->>F: JWT access + refresh tokens
    F-->>U: Login successful
```

## Gear Configuration & Calculation Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant AG as API Gateway
    participant GE as Gear Engine
    participant RD as Redis Cache
    participant DB as Database
    
    U->>F: Select components
    F->>AG: POST /api/calculate
    AG->>RD: Check cached result
    alt Cache hit
        RD-->>AG: Cached calculation
    else Cache miss
        AG->>GE: Calculate performance
        GE->>DB: Get component specs
        DB-->>GE: Component data
        GE->>GE: Perform calculations
        GE->>RD: Cache result
        GE-->>AG: Calculation results
    end
    AG-->>F: Performance data
    F-->>U: Display results
```

## Social Sharing Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant AG as API Gateway
    participant SS as Social Service
    participant IMG as Image Service
    participant SM as Social Media
    
    U->>F: Click share button
    F->>AG: POST /api/share/generate
    AG->>SS: Create share request
    SS->>IMG: Generate image
    IMG-->>SS: Image URL
    SS->>DB: Save share record
    SS-->>AG: Share code + image URL
    AG-->>F: Share ready
    F->>U: Show preview
    U->>F: Select platform
    F->>SM: Upload to platform
    SM-->>F: Share successful
    F-->>U: Confirmation
```