# External APIs

## Cloudinary API
- **Purpose:** Image generation, storage, and CDN delivery for social sharing
- **Documentation:** https://cloudinary.com/documentation
- **Base URL(s):** https://api.cloudinary.com/v1_1/{cloud_name}
- **Authentication:** API Key + API Secret
- **Rate Limits:** 5000 requests/hour, 75 concurrent transformations

**Key Endpoints Used:**
- `POST /image/upload` - Upload generated share images
- `POST /image/generate` - Create social media images from templates
- `GET /image/{public_id}` - Retrieve images for display

**Integration Notes:** Use SDK for TypeScript integration, implement retry logic for failed transformations, cache frequently accessed images

## Email Service API
- **Purpose:** User verification, password reset, and notification emails
- **Documentation:** Provider-specific (Resend, SendGrid, etc.)
- **Base URL(s):** Provider-specific API endpoint
- **Authentication:** API Key authentication
- **Rate Limits:** Provider-specific limits

**Key Endpoints Used:**
- `POST /emails/send` - Send verification and reset emails
- `POST /templates/render` - Render email templates
- `GET /emails/{id}/status` - Check email delivery status

**Integration Notes:** Implement queue-based sending for bulk emails, use transactional templates, handle bounce and complaint webhooks