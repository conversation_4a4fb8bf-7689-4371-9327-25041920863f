# Coding Standards

## Core Standards
- **Languages & Runtimes:** TypeScript 5.3.3, Node.js 20.11.0, Next.js 14.0.0
- **Style & Linting:** ESLint with TypeScript rules, Prettier for formatting
- **Test Organization:** Co-located test files with `*.test.ts` suffix

## Naming Conventions
| Element | Convention | Example |
|---------|------------|---------|
| React Components | PascalCase | BikeCard, GearCalculator |
| TypeScript Interfaces | PascalCase | IBikeConfiguration |
| Variables | camelCase | gearRatio, userId |
| Constants | SCREAMING_SNAKE_CASE | MAX_RETRIES, API_BASE_URL |
| Database Tables | snake_case | brompton_models, user_garage |
| API Endpoints | kebab-case | /api/garage/bikes, /auth/login |

## Critical Rules
- **Never use console.log in production code** - Use Winston logger instead
- **All API responses must use ApiResponse wrapper type** - Consistent response format
- **Database queries must use Prisma ORM, never raw SQL** - Type safety and security
- **All external inputs must be validated using Zod schemas** - Input sanitization
- **JWT tokens must be validated in middleware** - Security enforcement
- **Error messages must never expose sensitive data** - Information security

## Language-Specific Guidelines

### TypeScript Specifics
- **Strict Mode:** Always enable strict TypeScript configuration
- **Null Safety:** Use strict null checks and optional chaining
- **Type Definitions:** Create comprehensive type definitions for all data models
- **Error Types:** Define custom error types with proper typing

### React Specifics
- **Component Organization:** Use functional components with hooks
- **State Management:** Use Zustand for client state, React Query for server state
- **Performance:** Use React.memo and useMemo for expensive operations
- **Error Boundaries:** Implement error boundaries for component error handling