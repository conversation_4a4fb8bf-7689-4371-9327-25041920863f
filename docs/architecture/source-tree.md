# Source Tree

```
bromieworks/
├── packages/
│   ├── web/                          # Next.js frontend application
│   │   ├── app/                      # Next.js app router
│   │   │   ├── (auth)/               # Authenticated routes
│   │   │   │   ├── garage/           # Garage management
│   │   │   │   ├── models/           # Model browser
│   │   │   │   ├── profile/          # User profile
│   │   │   │   └── settings/         # App settings
│   │   │   ├── (public)/             # Public routes
│   │   │   │   ├── onboarding/       # New user onboarding
│   │   │   │   ├── browse/           # Model browsing
│   │   │   │   └── share/            # Shared configurations
│   │   │   ├── api/                  # API routes
│   │   │   │   ├── auth/             # Authentication endpoints
│   │   │   │   ├── models/           # Brompton model APIs
│   │   │   │   ├── garage/           # Garage management APIs
│   │   │   │   ├── calculations/     # Gear calculation APIs
│   │   │   │   └── share/            # Social sharing APIs
│   │   │   ├── globals.css           # Global styles
│   │   │   ├── layout.tsx            # Root layout
│   │   │   └── page.tsx              # Home page
│   │   ├── components/               # React components
│   │   │   ├── ui/                   # Base UI components
│   │   │   ├── forms/                # Form components
│   │   │   ├── garage/               # Garage-specific components
│   │   │   ├── models/               # Model browser components
│   │   │   └── charts/               # Chart components
│   │   ├── lib/                      # Utility libraries
│   │   │   ├── auth/                 # Authentication utilities
│   │   │   ├── api/                  # API client utilities
│   │   │   ├── calculations/         # Gear calculation utilities
│   │   │   ├── cache/                # Caching utilities
│   │   │   └── utils/                # General utilities
│   │   ├── hooks/                    # React hooks
│   │   ├── styles/                   # Styled components
│   │   ├── types/                    # TypeScript definitions
│   │   └── public/                   # Static assets
│   ├── server/                       # Server-side utilities
│   │   ├── services/                  # Business logic services
│   │   │   ├── auth/                 # Authentication service
│   │   │   ├── calculations/         # Gear calculation engine
│   │   │   ├── garage/               # Garage management
│   │   │   ├── models/               # Brompton data service
│   │   │   └── share/                # Social sharing service
│   │   ├── middleware/               # API middleware
│   │   ├── lib/                      # Server utilities
│   │   └── types/                    # Server types
│   └── shared/                       # Shared utilities and types
│       ├── types/                    # Shared TypeScript types
│       ├── utils/                    # Shared utilities
│       ├── constants/                # Shared constants
│       └── validation/               # Shared validation schemas
├── scripts/                          # Development and deployment scripts
│   ├── migrate.js                    # Database migration script
│   ├── seed.js                      # Database seeding script
│   ├── build.js                     # Build script
│   ├── deploy.js                    # Deployment script
│   └── test.js                      # Test runner script
├── docs/                             # Documentation
│   ├── architecture.md              # This architecture document
│   ├── prd.md                       # Product requirements
│   ├── front-end-spec.md            # Frontend specification
│   └── api/                         # API documentation
├── tests/                            # Test files
│   ├── unit/                        # Unit tests
│   ├── integration/                 # Integration tests
│   └── e2e/                         # End-to-end tests
├── .github/                          # GitHub Actions workflows
│   ├── ci.yml                       # Continuous integration
│   ├── cd.yml                       # Continuous deployment
│   └── security.yml                 # Security scanning
├── docker/                           # Docker configuration
│   ├── Dockerfile                   # Application Dockerfile
│   └── docker-compose.yml           # Local development
├── infrastructure/                   # Infrastructure as Code
│   ├── terraform/                   # Terraform configuration
│   └── vercel/                      # Vercel configuration
├── package.json                      # Root package.json
├── pnpm-workspace.yaml               # PNPM workspace configuration
├── tsconfig.json                     # TypeScript configuration
├── .eslintrc.json                    # ESLint configuration
├── .prettierrc.json                  # Prettier configuration
├── tailwind.config.js               # Tailwind CSS configuration
└── README.md                         # Project documentation
```