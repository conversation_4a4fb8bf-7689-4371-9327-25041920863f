# Security

## Input Validation
- **Validation Library:** Zod 3.22.0
- **Validation Location:** API route handlers before business logic
- **Required Rules:**
  - All external inputs MUST be validated
  - Validation at API boundary before processing
  - Whitelist approach preferred over blacklist

## Authentication & Authorization
- **Auth Method:** NextAuth.js 4.24.0 with JWT tokens
- **Session Management:** JWT access tokens with refresh token rotation
- **Required Patterns:**
  - Verify JWT tokens in middleware for protected routes
  - Implement rate limiting on authentication endpoints
  - Use secure, HTTP-only cookies for session management

## Secrets Management
- **Development:** Environment variables in `.env.local` files
- **Production:** Vercel environment variables with encrypted storage
- **Code Requirements:**
  - NEVER hardcode secrets
  - Access via configuration service only
  - No secrets in logs or error messages

## API Security
- **Rate Limiting:** Upstash Redis-based rate limiting with configurable limits
- **CORS Policy:** Strict CORS configuration for frontend domains only
- **Security Headers:** Security headers middleware (HSTS, CSP, XSS protection)
- **HTTPS Enforcement:** Automatic HTTPS redirection and secure cookies

## Data Protection
- **Encryption at Rest:** PostgreSQL encryption for sensitive data
- **Encryption in Transit:** TLS 1.3 for all communications
- **PII Handling:** Anonymization of user data in analytics, minimal PII collection
- **Logging Restrictions:** No sensitive data in logs, sanitized error messages

## Dependency Security
- **Scanning Tool:** Snyk or GitHub Dependabot
- **Update Policy:** Weekly security updates, immediate updates for critical vulnerabilities
- **Approval Process:** Security review for new dependencies with high risk scores

## Security Testing
- **SAST Tool:** ESLint security plugins, TypeScript security rules
- **DAST Tool:** OWASP ZAP automated scanning
- **Penetration Testing:** Quarterly penetration testing by security experts