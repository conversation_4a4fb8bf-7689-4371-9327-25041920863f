# Test Strategy and Standards

## Testing Philosophy
- **Approach:** Test-driven development for critical business logic
- **Coverage Goals:** 90% unit test coverage, 80% integration test coverage
- **Test Pyramid:** 70% unit tests, 20% integration tests, 10% E2E tests

## Test Types and Organization

### Unit Tests
- **Framework:** Jest 29.7.0
- **File Convention:** `*.test.ts` co-located with source files
- **Location:** Same directory as source files
- **Mocking Library:** ts-jest for TypeScript mocking
- **Coverage Requirement:** 90% for critical calculation logic, 80% for other modules

**AI Agent Requirements:**
- Generate tests for all public methods
- Cover edge cases and error conditions
- Follow AAA pattern (Arrange, Act, Assert)
- Mock all external dependencies

### Integration Tests
- **Scope:** API endpoints, database interactions, authentication flows
- **Location:** `tests/integration/`
- **Test Infrastructure:**
  - **Database:** In-memory SQLite for unit tests, Testcontainers PostgreSQL for integration
  - **Redis:** Mock Redis for unit tests, test Redis instance for integration
  - **External APIs:** WireMock for stubbing external services

### End-to-End Tests
- **Framework:** Playwright 1.40.0
- **Scope:** Critical user journeys (onboarding, garage management, configuration)
- **Environment:** Dockerized test environment with real services
- **Test Data:** Seeded test data with consistent state

## Test Data Management
- **Strategy:** Factory pattern with Faker.js for realistic test data
- **Fixtures:** `tests/fixtures/` directory with JSON fixtures
- **Factories:** Factory functions for creating test objects
- **Cleanup:** Automatic cleanup after each test using teardown hooks

## Continuous Testing
- **CI Integration:** Run unit tests on all PRs, integration tests on merge to main
- **Performance Tests:** Lighthouse CI for performance metrics
- **Security Tests:** OWASP ZAP security scanning on deployment