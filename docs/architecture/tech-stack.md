# Tech Stack

## Cloud Infrastructure
- **Provider:** Vercel (frontend & API) + Supabase (database) + Upstash (Redis)
- **Key Services:** Vercel Serverless Functions, Supabase PostgreSQL, Upstash Redis, Cloudinary CDN
- **Deployment Regions:** Global edge network with primary database in us-east-1

## Technology Stack Table
| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Language** | TypeScript | 5.3.3 | Primary development language | Strong typing for complex calculation logic and better developer experience |
| **Runtime** | Node.js | 20.11.0 | JavaScript runtime | LTS version with stable performance and wide ecosystem support |
| **Framework** | Next.js | 14.0.0 | Full-stack framework | App router, serverless functions, and React integration in one solution |
| **Database** | PostgreSQL | 15.0 | Primary data storage | Relational integrity for user data and configurations with strong ACID compliance |
| **ORM** | Prisma | 5.5.0 | Database access | Type-safe database operations with excellent migration support |
| **Cache** | Redis | 7.2 | Performance caching | High-speed caching for Brompton specifications and calculation results |
| **Auth** | NextAuth.js | 4.24.0 | Authentication | Complete authentication solution with JWT and session management |
| **API** | REST | OpenAPI 3.0 | API specification | Standardized, well-documented API with clear contracts |
| **Testing** | Jest | 29.7.0 | Unit/integration testing | Comprehensive testing framework with good TypeScript support |
| **Deployment** | Vercel | Latest | Hosting platform | Seamless Next.js integration with automatic scaling and CDN |
| **Storage** | Cloudinary | Latest | Image storage | Optimized for social media image generation and delivery |
| **Monitoring** | Vercel Analytics | Latest | Application monitoring | Integrated monitoring with deployment platform |