# Database Schema

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    preferences JSONB DEFAULT '{}'
);

-- Brompton models table
CREATE TABLE brompton_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    line VARCHAR(1) NOT NULL CHECK (line IN ('A', 'C', 'P', 'T', 'G')),
    type VARCHAR(50) NOT NULL,
    base_price DECIMAL(10,2),
    weight_kg DECIMAL(5,2),
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Components table
CREATE TABLE components (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('chainring', 'sprocket', 'tire', 'hub')),
    name VARCHAR(100) NOT NULL,
    specification JSONB NOT NULL,
    compatible_lines VARCHAR(1)[] CHECK (array_length(compatible_lines, 1) > 0),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Model components junction table
CREATE TABLE model_components (
    model_id UUID REFERENCES brompton_models(id) ON DELETE CASCADE,
    component_id UUID REFERENCES components(id) ON DELETE CASCADE,
    is_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (model_id, component_id)
);

-- User garage table
CREATE TABLE garage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    nickname VARCHAR(50) NOT NULL,
    brompton_model_id UUID REFERENCES brompton_models(id),
    is_primary BOOLEAN DEFAULT false,
    purchase_date DATE,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, nickname)
);

-- Bike configurations table
CREATE TABLE configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    garage_id UUID REFERENCES garage(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    chainring_id UUID REFERENCES components(id),
    sprocket_id UUID REFERENCES components(id),
    hub_id UUID REFERENCES components(id),
    tire_id UUID REFERENCES components(id),
    gear_ratio DECIMAL(10,4),
    development DECIMAL(10,4),
    speed_range JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Shared configurations table
CREATE TABLE shared_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    configuration_id UUID REFERENCES configurations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    share_code VARCHAR(10) UNIQUE NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT true,
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_brompton_models_line ON brompton_models(line);
CREATE INDEX idx_brompton_models_type ON brompton_models(type);
CREATE INDEX idx_components_type ON components(type);
CREATE INDEX idx_garage_user_id ON garage(user_id);
CREATE INDEX idx_garage_primary ON garage(user_id, is_primary) WHERE is_primary = true;
CREATE INDEX idx_configurations_garage_id ON configurations(garage_id);
CREATE INDEX idx_configurations_active ON configurations(garage_id, is_active) WHERE is_active = true;
CREATE INDEX idx_shared_configurations_code ON shared_configurations(share_code);
CREATE INDEX idx_shared_configurations_public ON shared_configurations(is_public, created_at) WHERE is_public = true;
CREATE INDEX idx_shared_configurations_user ON shared_configurations(user_id);

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_garage_updated_at BEFORE UPDATE ON garage
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_configurations_updated_at BEFORE UPDATE ON configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```