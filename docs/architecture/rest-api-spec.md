# REST API Spec

```yaml
openapi: 3.0.0
info:
  title: BromieWorks API
  version: 1.0.0
  description: API for BromieWorks Brompton bike calculator and garage management system
servers:
  - url: https://api.bromieworks.com/v1
    description: Production server
  - url: https://api-staging.bromieworks.com/v1
    description: Staging server

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        name:
          type: string
        avatar:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    BromptonModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        line:
          type: string
          enum: [A, C, P, T, G]
        type:
          type: string
        basePrice:
          type: number
          format: decimal
        weight:
          type: number
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    Component:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          enum: [chainring, sprocket, tire, hub]
        name:
          type: string
        specification:
          type: object
        compatibleLines:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
    
    Garage:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        nickname:
          type: string
        bromptonModelId:
          type: string
          format: uuid
        isPrimary:
          type: boolean
        purchaseDate:
          type: string
          format: date
          nullable: true
        notes:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    Configuration:
      type: object
      properties:
        id:
          type: string
          format: uuid
        garageId:
          type: string
          format: uuid
        name:
          type: string
        chainringId:
          type: string
          format: uuid
        sprocketId:
          type: string
          format: uuid
        hubId:
          type: string
          format: uuid
        tireId:
          type: string
          format: uuid
        gearRatio:
          type: number
        development:
          type: number
        speedRange:
          type: object
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    PerformanceCalculation:
      type: object
      properties:
        gearRatio:
          type: number
        development:
          type: number
        speedRange:
          type: object
          properties:
            min:
              type: number
            max:
              type: number
            optimal:
              type: number
        efficiency:
          type: number
        recommendations:
          type: array
          items:
            type: string

paths:
  /auth/register:
    post:
      summary: Register new user
      tags: [Authentication]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, name, password]
              properties:
                email:
                  type: string
                  format: email
                name:
                  type: string
                password:
                  type: string
                  minLength: 8
      responses:
        201:
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  accessToken:
                    type: string
                  refreshToken:
                    type: string
        400:
          description: Invalid input
        409:
          description: Email already exists

  /auth/login:
    post:
      summary: User login
      tags: [Authentication]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
      responses:
        200:
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  accessToken:
                    type: string
                  refreshToken:
                    type: string
        401:
          description: Invalid credentials

  /models:
    get:
      summary: Get all Brompton models
      tags: [Brompton Models]
      security: []
      parameters:
        - name: line
          in: query
          schema:
            type: string
            enum: [A, C, P, T, G]
        - name: type
          in: query
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
      responses:
        200:
          description: List of Brompton models
          content:
            application/json:
              schema:
                type: object
                properties:
                  models:
                    type: array
                    items:
                      $ref: '#/components/schemas/BromptonModel'
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      limit:
                        type: integer
                      total:
                        type: integer
                      totalPages:
                        type: integer

  /models/{id}:
    get:
      summary: Get specific Brompton model
      tags: [Brompton Models]
      security: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        200:
          description: Brompton model details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BromptonModel'
        404:
          description: Model not found

  /garage:
    get:
      summary: Get user's garage
      tags: [Garage]
      security:
        - BearerAuth: []
      responses:
        200:
          description: User's garage bikes
          content:
            application/json:
              schema:
                type: object
                properties:
                  bikes:
                    type: array
                    items:
                      $ref: '#/components/schemas/Garage'
    
    post:
      summary: Add bike to garage
      tags: [Garage]
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [nickname, bromptonModelId]
              properties:
                nickname:
                  type: string
                bromptonModelId:
                  type: string
                  format: uuid
                isPrimary:
                  type: boolean
                  default: false
                purchaseDate:
                  type: string
                  format: date
                notes:
                  type: string
      responses:
        201:
          description: Bike added to garage
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Garage'

  /calculate:
    post:
      summary: Calculate gear performance
      tags: [Calculations]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [chainringId, sprocketId, hubId, tireId]
              properties:
                chainringId:
                  type: string
                  format: uuid
                sprocketId:
                  type: string
                  format: uuid
                hubId:
                  type: string
                  format: uuid
                tireId:
                  type: string
                  format: uuid
      responses:
        200:
          description: Performance calculation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceCalculation'

  /share/generate:
    post:
      summary: Generate shareable image
      tags: [Social Sharing]
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [configurationId, template]
              properties:
                configurationId:
                  type: string
                  format: uuid
                template:
                  type: string
                  enum: [basic, detailed, comparison]
                title:
                  type: string
                description:
                  type: string
      responses:
        200:
          description: Share image generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  shareCode:
                    type: string
                  imageUrl:
                    type: string
                  expiresAt:
                    type: string
                    format: date-time
```