# Project Brief: BromieWorks

## Executive Summary

**BromieWorks** is a mobile-first Brompton bicycle assistant application that solves the critical problem of understanding and customizing Brompton performance characteristics through interactive gear ratio calculations and personalized analytics.

**Primary Problem Solved:** Brompton owners and potential buyers lack intuitive tools to visualize how different configurations affect bicycle performance, making it difficult to optimize setups for specific riding conditions and preferences.

**Target Market:** Current Brompton owners seeking performance optimization and potential buyers researching configuration options, particularly tech-savvy cyclists who value data-driven decision making.

**Key Value Proposition:** Transform complex gear ratio mathematics into engaging mobile experiences with card flips, swipe gestures, and real-time performance visualizations that make bicycle customization accessible and enjoyable.

## Problem Statement

**Current State:** Brompton owners and potential buyers face significant barriers when trying to understand bicycle performance characteristics. The technical complexity of gear ratios, combined with multiple configuration variables (chainrings, sprockets, hub gears, tire sizes), creates a steep learning curve that prevents users from making informed decisions about their bicycle setups.

**Key Pain Points:**
- **Information Asymmetry:** Users cannot easily visualize how different configurations affect real-world performance
- **Mathematical Complexity:** Gear ratio calculations require specialized knowledge (GR = CR / (RS * HGR) * 100%, DM = (AOD * 25.4 * π * GR) / 1000, SPD = (DM / 1000) * CAD * 60)
- **Decision Paralysis:** With 11 official models and multiple configuration options, users struggle to compare setups effectively
- **Lack of Mobile Tools:** Existing solutions are desktop-based spreadsheets or require technical expertise

**Impact:** This leads to suboptimal bicycle purchases, expensive post-purchase modifications, and underutilization of Brompton's versatile configuration system. Users miss out on performance benefits and cost savings.

**Existing Solutions Fall Short:** Current gear calculator apps lack Brompton-specific data, don't account for the unique hub gear systems, and fail to provide the mobile-first interactive experience needed for on-the-go decision making.

**Urgency:** With growing interest in urban cycling and micro-mobility, the timing is ideal for a solution that democratizes bicycle performance understanding.

## Proposed Solution

**Core Concept:** BromieWorks transforms complex Brompton performance calculations into an engaging mobile experience through React and Chakra UI, combining precise mathematical modeling with intuitive user interactions.

**Key Differentiators:**
- **Brompton-Specific Database:** Complete integration of official Brompton configurations, including the unique hub gear systems (BSR, BWR, SA8) and factory specifications
- **Mobile-First Interactive Calculations:** Real-time gear ratio computations with immediate visual feedback through swipe gestures and card flips
- **Personalized Garage System:** User-owned bicycle profiles enable performance tracking, configuration experimentation, and social sharing
- **Visual Performance Mapping:** Transforms abstract gear ratios into understandable speed and distance metrics

**Technical Innovation:** The solution leverages Chakra UI's mobile-optimized components alongside custom calculation engines that handle Brompton's specific gear ratio formulas, making sophisticated bicycle mechanics accessible through touch interfaces.

**Why This Solution Will Succeed:** Unlike generic bike calculators, BromieWorks addresses the complete Brompton ecosystem with factory-accurate data while delivering the mobile experience modern users expect. The focus on visualization and interactivity removes the technical barriers that prevent users from engaging with bicycle performance optimization.

**Long-term Vision:** The platform will evolve into a comprehensive Brompton community hub, connecting performance analytics with social features, maintenance tracking, and accessory recommendations.

## Target Users

### Primary User Segment: Current Brompton Owners

**Profile:** Tech-savvy urban cyclists aged 25-45, already owning a Brompton, comfortable with mobile apps, interested in optimizing their riding experience.

**Current Behaviors:**
- Research bicycle configurations through forums and YouTube videos
- Use generic bike gear calculators with limited Brompton support
- Experiment with different riding conditions and want to optimize performance
- Share their Brompton experiences on social media
- Seek to justify accessory purchases with performance data

**Specific Needs:**
- Understand how current configuration performs in different scenarios (commuting, leisure, fitness)
- Calculate performance impact of potential upgrades (different chainrings, tires)
- Track and analyze personal riding patterns and performance
- Share configurations and achievements with other enthusiasts
- Access performance data quickly during rides or shop visits

**Goals:** Maximize their Brompton investment, optimize for specific riding conditions, connect with like-minded enthusiasts, and make data-driven decisions about upgrades.

### Secondary User Segment: Potential Brompton Buyers

**Profile:** Urban professionals aged 28-50, researching premium folding bikes, evaluating Brompton against alternatives, value-conscious but willing to invest in quality.

**Current Behaviors:**
- Extensive online research comparing Brompton models (A, C, P, T, G lines)
- Visit physical stores for test rides
- Read reviews and watch comparison videos
- Calculate total cost of ownership including potential upgrades
- Seek validation from current owners

**Specific Needs:**
- Understand the practical differences between Brompton lines and models
- Evaluate which configuration best matches their riding style and commute
- Estimate performance characteristics for their specific use cases
- Justify the premium price point with performance data
- Visualize long-term value and upgrade possibilities

**Goals:** Make an informed purchase decision, choose the right model for their needs, understand the total cost of ownership, and feel confident in their investment.

## Goals & Success Metrics

### Business Objectives

- **Establish BromieWorks as the premier Brompton mobile app:** Achieve #1 ranking in app stores for "Brompton" and "folding bike" searches within 6 months of launch
- **Build engaged user community:** Reach 10,000 active monthly users by end of year 1, with 25% weekly retention rate
- **Demonstrate technical capabilities:** Showcase successful React + Chakra UI mobile development with complex calculations and smooth animations
- **Create monetization pathway:** Develop user base and engagement metrics to attract potential acquisition by cycling tech companies or Brompton themselves

### User Success Metrics

- **Configuration Understanding:** Users can successfully identify optimal gear setups for their riding style 80% faster than traditional research methods
- **Decision Confidence:** 90% of users report increased confidence in their Brompton purchase or upgrade decisions after using the app
- **Time to Value:** New users can perform meaningful gear ratio comparisons within 3 minutes of first use
- **Social Engagement:** 40% of users share their garage configurations or performance reports at least once per month

### Key Performance Indicators (KPIs)

- **User Acquisition:** 500 new installs per month by month 3, growing to 2,000 per month by month 12
- **User Engagement:** Average session duration of 8+ minutes, with 3+ sessions per week per active user
- **Feature Adoption:** 70% of registered users create at least one garage profile, 50% use configuration tools weekly
- **Performance Accuracy:** Gear calculation results match manufacturer specifications with 99.9% accuracy
- **App Performance:** <2 second load time, 60fps animations, 99.5% crash-free sessions
- **Social Sharing:** 15% of garage configurations shared externally, driving 30% of new user acquisition

## MVP Scope

### Core Features (Must Have)

- **User Authentication System:** Email-based registration with verification codes, secure login, and user profile management
- **Complete Brompton Database:** All 11 official models with accurate specifications for chainrings, rear sprockets, hub gears, and tire configurations
- **Interactive Gear Calculator:** Real-time performance calculations using Brompton-specific formulas (GR = CR / (RS * HGR) * 100%, DM = (AOD * 25.4 * π * GR) / 1000, SPD = (DM / 1000) * CAD * 60)
- **Mobile-First UI with Chakra UI:** Responsive design optimized for touch interactions, swipe gestures, and card flips
- **User Garage System:** Ability to add, nickname, and manage multiple Brompton configurations with performance comparisons
- **Configuration Customization:** Interactive controls for modifying chainrings, tires, hub gears, and cadence with live performance updates
- **Social Sharing Features:** Generate shareable images of garage configurations with model details and performance specs
- **Onboarding Flow:** Guided experience for new users including line selection and model choice with swipe interactions

### Out of Scope for MVP

- **Ride Tracking and History:** GPS-based ride recording, distance tracking, and historical performance analysis (planned for Version 2)
- **Advanced Analytics:** Detailed statistics, trends, and performance insights over time
- **Community Features:** User profiles, forums, comments, and social interactions beyond sharing
- **Maintenance Tracking:** Service reminders, maintenance logs, and parts replacement tracking
- **Accessory Integration:** Compatibility checking and recommendations for third-party accessories
- **Multi-language Support:** Localization beyond English/Chinese
- **Offline Mode:** Full functionality without internet connection
- **Advanced Custom Components:** Support for non-Brompton aftermarket parts

### MVP Success Criteria

**Definition of MVP Success:** A fully functional mobile application that allows users to register, explore Brompton models, create personalized garage profiles, perform accurate gear calculations, and share configurations socially. The app must demonstrate smooth performance, calculation accuracy, and intuitive mobile interactions that showcase React + Chakra UI capabilities.

**Technical Validation:** All gear ratio calculations must match manufacturer specifications, animations must maintain 60fps, and the app must perform well on mid-range mobile devices.

**User Validation:** Beta users can successfully navigate the entire user journey from registration to sharing a configuration within 10 minutes, reporting high confidence in their understanding of Brompton performance differences.

## Post-MVP Vision

### Phase 2 Features

**Ride Tracking & Analytics**
- GPS-based ride recording with automatic distance and time tracking
- Performance analytics comparing actual vs. calculated performance metrics
- Historical ride data visualization with trends and insights
- Personalized riding recommendations based on collected data

**Advanced Configuration Tools**
- Side-by-side comparison of multiple bike configurations
- "What-if" scenario modeling for potential upgrades
- Integration with weather and terrain data for performance predictions
- Custom component support for aftermarket parts

**Community & Social Features**
- User profiles with riding achievements and garage showcases
- Community forums for configuration advice and riding tips
- Group rides and events organization
- Configuration sharing with detailed build specifications

### Long-term Vision

**Brompton Ecosystem Platform**
- Evolve into the central hub for all Brompton-related digital experiences
- Integration with Brompton dealers for inventory and test ride scheduling
- Maintenance tracking with service reminders and dealer integration
- Accessory marketplace with compatibility verification

**AI-Powered Personalization**
- Machine learning algorithms for personalized configuration recommendations
- Route optimization based on individual bike performance characteristics
- Predictive maintenance alerts based on riding patterns and component wear
- Adaptive performance predictions based on riding style and conditions

### Expansion Opportunities

**Geographic Expansion**
- Multi-language support for European and Asian markets
- Localization for regional Brompton model variations
- Integration with local cycling communities and dealer networks

**Platform Diversification**
- Desktop web application for detailed configuration planning
- Apple Watch and Wear OS companion apps for ride tracking
- Integration with popular cycling apps (Strava, Komoot, Ride with GPS)
- API for third-party developers and cycling tech companies

**Business Model Evolution**
- Premium subscription for advanced analytics and AI features
- Partnership revenue from Brompton dealers and accessory manufacturers
- Data licensing insights to urban planning and micro-mobility companies
- White-label solutions for other bicycle manufacturers

## Technical Considerations

### Platform Requirements

**Target Platforms:**
- **Primary:** Mobile web application (Progressive Web App - PWA) for universal access
- **Secondary:** React Native wrapper for App Store/Play Store distribution
- **Fallback:** Responsive web application for desktop users

**Browser/OS Support:**
- **Mobile:** iOS 14+, Android 8+ with modern browser support
- **Desktop:** Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **PWA Features:** Offline capability, installable home screen icons, push notifications

**Performance Requirements:**
- **Load Time:** <2 seconds initial load, <500ms subsequent interactions
- **Animation Performance:** 60fps for all transitions and card flips
- **Calculation Speed:** <100ms for complex gear ratio computations
- **Memory Usage:** <50MB RAM usage on mid-range mobile devices

### Technology Preferences

**Frontend:**
- **Framework:** React 18+ with TypeScript for type safety and performance
- **UI Library:** Chakra UI for responsive, accessible components and design system
- **Animation:** Framer Motion for smooth card flips, swipe gestures, and transitions
- **State Management:** React Query for server state, Zustand for client state
- **PWA:** Workbox for offline caching and service worker management

**Backend:**
- **Database:** PostgreSQL for relational data (users, configurations), Redis for caching
- **API:** Node.js with Express.js or Next.js API routes for serverless deployment
- **Authentication:** JWT tokens with email verification and password reset
- **File Storage:** AWS S3 or Cloudinary for user-generated images and shared content

### Architecture Considerations

**Repository Structure:** Monorepo with separate packages for frontend, backend, and shared types

**Service Architecture:** Microservices-ready design with clear API boundaries

**Integration Requirements:** RESTful APIs with potential for GraphQL in future iterations

**Security/Compliance:** GDPR compliance, data encryption, secure authentication flows

**Mobile-Specific Considerations:**
- **Touch Interactions:** Custom gesture handlers for swipe navigation and card flips
- **Responsive Design:** Mobile-first approach with breakpoints for tablet/desktop
- **Performance Optimization:** Code splitting, lazy loading, and optimized bundle sizes
- **Offline Support:** Service worker caching for core functionality and Brompton data

## Constraints & Assumptions

### Constraints

**Budget:**
- **Development Budget:** Self-funded or bootstrapped approach with minimal external investment
- **Infrastructure Costs:** Must remain under $100/month for initial hosting and services
- **Marketing Budget:** Organic growth through social sharing and community engagement
- **Contingency:** 20% buffer for unexpected technical challenges or scope adjustments

**Timeline:**
- **MVP Development:** 3-4 months for initial release with core features
- **Beta Testing:** 1 month with select Brompton owners for validation
- **Public Launch:** Target launch within 6 months of project start
- **Post-Launch:** 2 months for bug fixes and initial feature refinements

**Resources:**
- **Development Team:** Solo developer or small team (2-3 people maximum)
- **Design Resources:** Limited to Chakra UI design system with custom branding
- **Content Creation:** User-generated content with minimal editorial oversight
- **Technical Support:** Community-based support with minimal dedicated resources

**Technical:**
- **Third-Party Dependencies:** Must use stable, well-supported libraries with clear licensing
- **Data Sources:** Brompton specifications must be manually curated and verified
- **Performance:** Cannot rely on constant high-speed internet connections
- **Device Compatibility:** Must support mid-range mobile devices from the last 3-4 years

### Key Assumptions

- Users have basic familiarity with Brompton bicycles and cycling terminology
- Target users own smartphones capable of running modern web applications
- Brompton specifications remain relatively stable and don't change frequently
- Users are willing to provide email addresses for registration and sharing
- Social sharing features will drive organic user acquisition
- Gear calculation accuracy is more important than advanced visualizations
- Mobile users prefer quick interactions over detailed desktop-style interfaces
- Community engagement will be sufficient for support and feedback
- Users will find value in performance comparisons even without physical test rides

## Risks & Open Questions

### Key Risks

- **Technical Risk: Complex Animation Performance:** Card flips and swipe gestures may not achieve 60fps on older mobile devices, leading to poor user experience
- **Market Risk: Niche Audience Size:** The Brompton owner market may be too small to achieve sustainable user growth without broader cycling appeal
- **Data Risk: Specification Accuracy:** Incorrect gear ratio calculations or outdated Brompton specifications could erode user trust and damage app credibility
- **Competitive Risk: Incumbent Solutions:** Established cycling apps (Strava, Komoot) could add Brompton-specific features, making differentiation difficult
- **User Adoption Risk: Value Proposition Clarity:** Users may not perceive enough value in gear calculations versus existing research methods
- **Technical Debt Risk: Rapid Development:** Pressure to launch quickly may lead to code quality issues that hinder future development

### Open Questions

- What is the actual size of the active Brompton owner community that would use such an app?
- How do potential buyers currently research Brompton configurations, and what pain points do they experience?
- What level of calculation accuracy do users require for decision making?
- Which social features will drive the most organic growth and engagement?
- How important are offline capabilities versus always-online functionality?
- What pricing model (if any) would users accept for premium features?
- How can we validate gear calculation accuracy without access to physical bikes for testing?
- What are the most common configuration questions that Brompton owners have?

### Areas Needing Further Research

- **Competitive Landscape:** Deep analysis of existing bike calculator apps and their limitations
- **User Behavior Patterns:** How Brompton owners currently research and share configuration information
- **Technical Validation:** Testing animation performance on target mobile devices
- **Data Sourcing:** Establishing reliable channels for Brompton specification updates
- **Community Building:** Identifying key Brompton communities and influencers for partnerships
- **Monetization Pathways:** Researching successful freemium models in cycling/fitness apps
- **International Expansion:** Understanding regional Brompton market differences and requirements

## Appendices

### A. Research Summary

**Available Research Materials:**
- Comprehensive Brompton specification database with 11 official models
- Detailed gear ratio calculation formulas and algorithms
- User interface specifications for mobile-first design
- Complete user stories and feature requirements
- Technical architecture considerations for React + Chakra UI

**Key Insights from Requirements Analysis:**
- The market gap for Brompton-specific performance tools is well-defined
- Mobile-first approach with interactive animations is a key differentiator
- Social sharing and garage features create user engagement opportunities
- Technical complexity of gear calculations presents both challenge and opportunity
- Version 2 planning (ride tracking) provides clear growth pathway

**Competitive Intelligence Gaps:**
- Limited analysis of existing bike calculator apps and their limitations
- Need for deeper understanding of Brompton community preferences
- User behavior patterns around bicycle research and configuration sharing

### B. Stakeholder Input

**Project Origin:** This project emerges from a clear need to demonstrate React + Chakra UI mobile development capabilities while solving a genuine problem for Brompton enthusiasts.

**Key Requirements:** The project must balance technical demonstration with practical utility, ensuring the gear calculations are accurate and the mobile experience is engaging.

**Success Criteria:** Beyond technical implementation, success will be measured by user engagement and the app's ability to simplify complex bicycle performance concepts.

### C. References

**Technical Resources:**
- React 18+ documentation and TypeScript guides
- Chakra UI component library and design system
- Framer Motion for animations and gesture handling
- Brompton official specifications and technical documentation
- Gear ratio calculation formulas and bicycle physics principles

**Design Resources:**
- Mobile-first UI/UX best practices
- Touch interaction patterns and gesture design
- Data visualization techniques for performance metrics
- Social sharing patterns and engagement strategies

**Project Management:**
- Agile development methodologies for MVP delivery
- User testing and validation approaches
- Performance optimization strategies for mobile applications

## Next Steps

### Immediate Actions

1. **Competitive Analysis:** Research existing bike calculator apps to identify gaps and opportunities for differentiation
2. **Technical Validation:** Create proof-of-concept for gear ratio calculations and mobile animations using React + Chakra UI
3. **User Research:** Conduct interviews with Brompton owners to validate pain points and feature priorities
4. **Data Sourcing:** Establish reliable method for obtaining and maintaining accurate Brompton specifications
5. **Architecture Setup:** Initialize development environment with React, TypeScript, Chakra UI, and chosen backend stack

### PM Handoff

This Project Brief provides the full context for **BromieWorks**. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PRD section by section as the template indicates, asking for any necessary clarification or suggesting improvements.

**Key Handoff Notes:**
- Focus on MVP features: user authentication, Brompton database, gear calculator, garage system, mobile UI, and social sharing
- Prioritize mobile-first design with smooth animations (card flips, swipe gestures)
- Emphasize calculation accuracy and performance optimization
- Consider technical constraints (budget, timeline, small team) when planning implementation
- Validate assumptions through user research before full development commitment