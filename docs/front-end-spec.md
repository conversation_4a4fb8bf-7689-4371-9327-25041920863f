# BromieWorks UI/UX Specification

## Introduction

This document defines the user experience goals, information architecture, user flows, and visual design specifications for BromieWorks' user interface. It serves as the foundation for visual design and frontend development, ensuring a cohesive and user-centered experience.

### Overall UX Goals & Principles

#### Target User Personas

**Tech-Savvy Brompton Owner (Primary):**
- 25-45 years old, urban professional
- Owns a Brompton, comfortable with mobile apps
- Seeks performance optimization and data-driven decisions
- Values both technical accuracy and engaging UX
- Active in cycling communities, likely to share on social media

**Potential Brompton Buyer (Secondary):**
- 28-50 years old, researching premium folding bikes
- Evaluating Brompton against alternatives
- Needs clear understanding of model differences
- Requires confidence in purchase decisions
- Values detailed specifications and performance data

**Casual Cycling Enthusiast (Tertiary):**
- Interested in bicycle performance generally
- May not own a Brompton but curious about the brand
- Appreciates well-designed, educational apps
- Likely to explore features without immediate purchase intent

#### Usability Goals

- **Time-to-Value:** New users can perform meaningful gear comparisons within 3 minutes
- **Learning Curve:** Minimal instruction needed for core navigation and features
- **Efficiency:** Power users can quickly access and modify configurations
- **Error Prevention:** Clear validation for incompatible component combinations
- **Accessibility:** WCAG 2.1 AA compliance for diverse user needs

#### Design Principles

1. **Data Clarity Above All** - Complex gear ratios must be presented in intuitive, understandable ways
2. **Mobile-First Interactions** - Every design decision prioritizes touch interfaces and mobile contexts
3. **Progressive Disclosure** - Simple interfaces that reveal complexity as needed
4. **Performance as a Feature** - Smooth 60fps animations and instant feedback build user trust
5. **Brompton-Inspired Aesthetics** - Design language that reflects the brand's heritage and quality

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | Initial UI/UX specification creation | Sally (UX Expert) |

## Information Architecture (IA)

### Site Map / Screen Inventory

```mermaid
graph TD
    A[Launch Screen] --> B[Authentication]
    B --> C[Onboarding Flow]
    C --> D[Garage/Home]
    
    B --> E[Login]
    E --> D
    
    D --> F[Configuration Sub-page]
    D --> G[Model Browser]
    D --> H[Profile/Settings]1
    
    
    G --> I[Model Detail]
    G --> J[Add to Garage]
    
    F --> K[Gear Calculator]
    F --> L[Performance Results]
    
    H --> M[Edit Profile]
    H --> N[App Settings]
    
    I --> O[Performance Charts]
    I --> P[Comparison Tools]
    
    D --> Q[Share Generation]
    Q --> R[Social Media Share]
    
    D --> S[Riding Records]
    S --> T[Ride Analytics]
```

### Navigation Structure

**Primary Navigation:** Bottom navigation bar with three main sections: Models (车型), Garage (车库), and Profile (我的). The Garage section is highlighted as the primary home screen.

**Secondary Navigation:** Contextual navigation within each section including back buttons, action buttons (Configure, Share, Add to Garage), and tab interfaces for different data views.

**Breadcrumb Strategy:** Simple linear navigation within flows (Onboarding → Line Selection → Model Selection → Garage) with clear back navigation and progress indicators during multi-step processes.

## User Flows

### Flow 1: First-Time User Onboarding

**User Goal:** Complete registration and initial bike setup to start using the app

**Entry Points:** App launch screen with email registration

**Success Criteria:** User successfully registers, selects their Brompton model, and lands in their garage with a configured bike

#### Flow Diagram
```mermaid
graph TD
    A[Launch App] --> B[Email Registration]
    B --> C[Verification Code]
    C --> D[Line Selection]
    D --> E[Model Selection]
    E --> F[Complete Setup]
    F --> G[Garage Home]
    
    D --> H[Swipe Between Lines]
    E --> I[Swipe Between Models]
```

#### Edge Cases & Error Handling:
- Invalid email format during registration
- Verification code expiration or incorrect entry
- Network connectivity issues during verification
- User skips model selection (require selection to proceed)
- App interruption during onboarding process

#### Notes:
This flow is critical for user retention and should be optimized for completion. The swipe gestures for line and model selection should feel natural and engaging.

### Flow 2: Gear Configuration & Performance Calculation

**User Goal:** Customize bike components and calculate performance metrics

**Entry Points:** Garage page → Configure button, Model browser → Add to Garage

**Success Criteria:** User successfully modifies components, sees real-time performance updates, and saves configuration

#### Flow Diagram
```mermaid
graph TD
    A[Garage/Model Detail] --> B[Configure Button]
    B --> C[Component Selection]
    C --> D[Real-time Calculation]
    D --> E[Performance Display]
    E --> F[Save Configuration]
    
    C --> G[Chainring Selection]
    C --> H[Tire Selection]
    C --> I[Hub Gear Selection]
    C --> J[Calculation Metrics]
```

#### Edge Cases & Error Handling:
- Incompatible component combinations
- Invalid input values for custom components
- Calculation errors due to missing data
- Network issues during save operations
- Component data synchronization failures

#### Notes:
Real-time calculation performance is crucial for user experience. Consider caching strategies for frequently used calculations.

### Flow 3: Social Sharing & Community Features

**User Goal:** Share bike configurations and discover community setups

**Entry Points:** Garage share button, Configuration completion, Community section

**Success Criteria:** User generates shareable image, shares to social platforms, and receives engagement

#### Flow Diagram
```mermaid
graph TD
    A[Garage/Config] --> B[Share Button]
    B --> C[Image Generation]
    C --> D[Preview & Customize]
    D --> E[Platform Selection]
    E --> F[Share/Save]
    
    B --> G[Community Browse]
    G --> H[Discover Configs]
    H --> I[Copy to Garage]
```

#### Edge Cases & Error Handling:
- Image generation failures
- Social media API limitations
- Privacy concerns with shared data
- Large image file sizes for mobile sharing
- Platform-specific formatting requirements

#### Notes:
Social sharing is a key growth driver. Ensure generated images are visually appealing and include proper attribution.

### Flow 4: Garage Management & Multiple Bikes

**User Goal:** Manage multiple bike configurations and compare performance

**Entry Points:** Garage home page, Model browser add actions

**Success Criteria:** User can add, edit, and compare multiple bike configurations effectively

#### Flow Diagram
```mermaid
graph TD
    A[Garage Home] --> B[Add New Bike]
    A --> C[Edit Existing Bike]
    A --> D[Delete Bike]
    A --> E[Compare Bikes]
    
    B --> F[Select Model Type]
    F --> G[Customize Configuration]
    G --> H[Save to Garage]
    
    C --> I[Modify Components]
    I --> J[Update Performance]
    J --> K[Save Changes]
```

#### Edge Cases & Error Handling:
- Maximum garage size limits
- Data loss during configuration updates
- Sync issues across multiple devices
- Duplicate bike configurations
- Performance comparison calculation errors

#### Notes:
Garage management is a core retention feature. Implement robust data backup and synchronization strategies.

## Wireframes & Mockups

### Design Files
**Primary Design Files:** Figma design system with mobile-first component library and interaction prototypes

### Key Screen Layouts

#### Launch & Authentication Screen
**Purpose:** First impression and user entry point
**Key Elements:**
- App logo and branding
- Email input field with validation
- Clean, minimal design focusing on conversion
- Loading states and error messaging
**Interaction Notes:** Simple form with keyboard-optimized input
**Design File Reference:** Figma frame: Authentication/01-Launch

#### Onboarding Flow Screens
**Purpose:** Guided model selection and app introduction
**Key Elements:**
- Line selection cards with swipe navigation
- Model detail cards with key specifications
- Progress indicators and clear CTAs
- Educational tooltips for features
**Interaction Notes:** Swipe gestures with momentum, card tap selection
**Design File Reference:** Figma frame: Onboarding/02-LineSelection

#### Garage Home Screen
**Purpose:** Primary user dashboard showing bike collection
**Key Elements:**
- Bike cards with images and nicknames
- Performance metrics overview
- Quick action buttons (Configure, Share)
- Bottom navigation with active state
**Interaction Notes:** Card tap for details, swipe for additional bikes
**Design File Reference:** Figma frame: Garage/03-Home

#### Configuration Interface
**Purpose:** Interactive bike customization and performance calculation
**Key Elements:**
- Component selection dropdowns
- Real-time performance calculation display
- Gear ratio matrix with clear formatting
- Save/reset action buttons
**Interaction Notes:** Immediate feedback on changes, smooth transitions
**Design File Reference:** Figma frame: Configuration/04-Build

#### Model Browser
**Purpose:** Discovery and research of Brompton models
**Key Elements:**
- Filter and search controls
- Model grid with images and specs
- Add to garage functionality
- Comparison tools
**Interaction Notes:** Infinite scroll, quick preview modals
**Design File Reference:** Figma frame: Browse/05-Models

## Component Library / Design System

### Design System Approach
**Design System Approach:** Custom design system built on Chakra UI foundation with Brompton-inspired theming and mobile-optimized components

### Core Components

#### Bike Card Component
**Purpose:** Display bike information with interactive capabilities
**Variants:** Garage card, Browser card, Comparison card
**States:** Default, Selected, Disabled, Loading
**Usage Guidelines:** Use consistent image ratios, always include key specs, maintain touch-friendly tap targets

#### Gear Calculator Component
**Purpose:** Real-time performance calculation interface
**Variants:** Basic calculator, Advanced calculator, Comparison view
**States:** Idle, Calculating, Results, Error
**Usage Guidelines:** Prioritize calculation speed, clear error states, responsive to input changes

#### Swipe Carousel Component
**Purpose:** Gesture-based navigation for bike/model browsing
**Variants:** Full screen, Inline carousel, Multi-item carousel
**States:** Ready, Swiping, Settling, Disabled
**Usage Guidelines:** Implement momentum physics, provide visual feedback, include accessibility fallbacks

#### Configuration Selector Component
**Purpose:** Component selection with validation and compatibility
**Variants:** Dropdown, Radio group, Multi-select
**States:** Available, Selected, Incompatible, Disabled
**Usage Guidelines:** Clear compatibility indicators, smooth animations, keyboard accessible

#### Performance Chart Component
**Purpose:** Visual representation of gear data and performance metrics
**Variants:** Line chart, Bar chart, Comparison chart
**States:** Loading, Ready, Interactive, Export mode
**Usage Guidelines:** Mobile-optimized touch interactions, clear legends, responsive sizing

## Branding & Style Guide

### Visual Identity
**Brand Guidelines:** Modern cycling aesthetic with Brompton heritage colors and technical precision

### Color Palette
| Color Type | Hex Code | Usage |
|------------|----------|-------|
| Primary | #1a5f3f | Primary buttons, active states, brand elements |
| Secondary | #2c3e50 | Secondary text, borders, backgrounds |
| Accent | #e74c3c | Highlights, alerts, important actions |
| Success | #27ae60 | Positive feedback, confirmations, save states |
| Warning | #f39c12 | Cautions, important notices, warnings |
| Error | #e74c3c | Errors, destructive actions, validation |
| Neutral | #ecf0f1, #95a5a6, #34495e | Backgrounds, text, borders, UI elements |

### Typography
#### Font Families
- **Primary:** Inter (clean, modern sans-serif for UI)
- **Secondary:** Roboto Mono (technical data display)
- **Monospace:** JetBrains Mono (code and precise calculations)

#### Type Scale
| Element | Size | Weight | Line Height |
|---------|------|--------|------------|
| H1 | 28px | 700 | 1.2 |
| H2 | 24px | 600 | 1.3 |
| H3 | 20px | 600 | 1.4 |
| Body | 16px | 400 | 1.5 |
| Small | 14px | 400 | 1.4 |

### Iconography
**Icon Library:** Custom bicycle and gear icons + Material Design icons
**Usage Guidelines:** Consistent stroke weight, clear meaning, accessible labels

### Spacing & Layout
**Grid System:** 8px grid system with responsive breakpoints
**Spacing Scale:** 4px, 8px, 16px, 24px, 32px, 48px, 64px

## Accessibility Requirements

### Compliance Target
**Standard:** WCAG 2.1 AA compliance for all interactive elements

### Key Requirements
**Visual:**
- Color contrast ratios: 4.5:1 for normal text, 3:1 for large text
- Focus indicators: Visible focus rings with 2px minimum width
- Text sizing: Responsive text with 200% zoom support

**Interaction:**
- Keyboard navigation: Full keyboard access with logical tab order
- Screen reader support: Proper ARIA labels and live regions
- Touch targets: Minimum 44x44px tap targets with adequate spacing

**Content:**
- Alternative text: Descriptive alt text for all images
- Heading structure: Proper H1-H6 hierarchy for screen readers
- Form labels: Associated labels for all form inputs

### Testing Strategy
Automated accessibility testing with axe-core, manual testing with screen readers, and user testing with accessibility needs

## Responsiveness Strategy

### Breakpoints
| Breakpoint | Min Width | Max Width | Target Devices |
|------------|-----------|-----------|----------------|
| Mobile | 320px | 767px | Smartphones (iOS, Android) |
| Tablet | 768px | 1023px | Tablets (iPad, Android tablets) |
| Desktop | 1024px | 1439px | Laptop and desktop computers |
| Wide | 1440px | - | Large desktop monitors |

### Adaptation Patterns
**Layout Changes:** Stack vertically on mobile, side-by-side on desktop, flexible grids that adapt to screen size
**Navigation Changes:** Bottom navigation on mobile, sidebar or top navigation on desktop
**Content Priority:** Essential content first on mobile, additional information revealed on larger screens
**Interaction Changes:** Touch-optimized on mobile, hover states and keyboard shortcuts on desktop

## Animation & Micro-interactions

### Motion Principles
Physics-based animations with natural easing, performance-optimized for 60fps, accessible with reduced motion options

### Key Animations
- **Card Flip:** 3D rotation for bike details (Duration: 400ms, Easing: cubic-bezier(0.4, 0.0, 0.2, 1))
- **Swipe Navigation:** Momentum-based scrolling with bounce (Duration: 300ms, Easing: cubic-bezier(0.25, 0.46, 0.45, 0.94))
- **Button Press:** Scale and color feedback (Duration: 150ms, Easing: ease-out)
- **Data Update:** Smooth transitions for calculation results (Duration: 200ms, Easing: ease-in-out)

## Performance Considerations

### Performance Goals
- **Page Load:** <2 seconds initial app load
- **Interaction Response:** <500ms for all user interactions
- **Animation FPS:** 60fps for all animations and transitions

### Design Strategies
Code splitting for route-based loading, lazy loading for images and charts, optimized assets with modern formats, caching strategies for frequently accessed data

## Next Steps

### Immediate Actions
1. Review and validate all user flows with stakeholders
2. Create detailed visual designs in Figma based on this specification
3. Develop interactive prototypes for key animations and gestures
4. Conduct usability testing with target user personas
5. Finalize component library and design system implementation

### Design Handoff Checklist
- [ ] All user flows documented and validated
- [ ] Component inventory complete with variants and states
- [ ] Accessibility requirements defined and testable
- [ ] Responsive strategy clear with breakpoint specifications
- [ ] Brand guidelines incorporated into design system
- [ ] Performance goals established and achievable