# BromieWorks PRD Sections

This document contains the complete Product Requirements Document (PRD) for BromieWorks, organized into individual sections for easier navigation and maintenance.

## Sections

### Introduction & Foundation
- [Goals and Background Context](./goals-and-background-context.md) - Project objectives, background context, and change log
- [Requirements](./requirements.md) - Functional and non-functional requirements for the MVP
- [User Interface Design Goals](./user-interface-design-goals.md) - UX vision, interaction paradigms, and design specifications
- [Technical Assumptions](./technical-assumptions.md) - Architecture decisions, technology stack, and infrastructure choices

### Development Planning
- [Epic List](./epic-list.md) - High-level overview of all development epics

### Detailed Epics
- [Epic 1: Foundation & Authentication](./epic-1-foundation-authentication.md) - Project setup, authentication, and core infrastructure
- [Epic 2: Brompton Database & Gear Calculator](./epic-2-brompton-database-gear-calculator.md) - Core data models and calculation engine
- [Epic 3: User Garage & Configuration System](./epic-3-user-garage-configuration-system.md) - Personal bike management and configuration
- [Epic 4: Mobile UI & Interactive Features](./epic-4-mobile-ui-interactive-features.md) - Mobile-first interface with gestures and animations
- [Epic 5: Social Sharing & Onboarding](./epic-5-social-sharing-onboarding.md) - Social features and user onboarding

### Next Steps
- [Next Steps](./next-steps.md) - UX and architecture prompts for the development team

---

## Document Structure

Each section file contains:
- Proper heading levels (adjusted from original ## to #)
- Complete content including all subsections
- Preserved formatting, tables, and special characters
- Code blocks and technical specifications

The original PRD document at `/docs/prd.md` remains the source of truth, while these sectioned files provide easier navigation for team members working on specific areas.