# Next Steps

## UX Expert Prompt

**For UX Expert:** Design the mobile-first user experience for BromieWorks, focusing on:
- Intuitive swipe gestures and card flip animations for bike browsing
- Clean, modern interface that makes complex gear data accessible
- Engaging onboarding flow that demonstrates key features
- Social sharing interfaces that drive organic growth
- Accessibility compliance with WCAG 2.1 AA standards
- Performance optimization for 60fps animations on mobile devices

Use the PRD requirements and UI design goals section as your foundation, creating detailed wireframes and interaction patterns that showcase React + Chakra UI capabilities.

## Architect Prompt

**For Architect:** Design the technical architecture for BromieWorks using the specifications in this PRD:
- Monorepo structure with React 18+, TypeScript, and Chakra UI
- Serverless backend with Next.js API routes and PostgreSQL
- High-performance gear calculation engine with caching strategies
- Mobile-optimized PWA with service worker and offline capabilities
- Social image generation and sharing infrastructure
- Security and privacy compliance with GDPR requirements
- CI/CD pipeline with automated testing and deployment

Focus on creating a scalable, maintainable architecture that can deliver the MVP features while supporting future growth, with special attention to calculation accuracy and mobile performance.