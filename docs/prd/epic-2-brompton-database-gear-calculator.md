# Epic 2: Brompton Database & Gear Calculator

**Epic Goal:** Build the complete Brompton model database and implement the core gear calculation engine that powers the app's primary value proposition, delivering accurate performance data for all configurations.

## Story 2.1: Brompton Data Model & Seeding

**As a** developer,  
**I want** a comprehensive data model for all Brompton specifications and automated seeding scripts,  
**so that** the app has accurate, complete data for all official models and configurations.

**Acceptance Criteria:**
1. Complete data model for Brompton lines (A, C, P, T, G) and their variants
2. Component specifications for chainrings, rear sprockets, hub gears, and tires
3. Configuration compatibility rules and validation logic
4. Automated seeding scripts with data from official Brompton specifications
5. Data validation to ensure consistency and accuracy
6. Admin interface for manual data updates and additions
7. Version tracking for specification changes over time

## Story 2.2: Gear Calculation Engine

**As a** user,  
**I want** accurate real-time gear ratio calculations for any Brompton configuration,  
**so that** I can understand how different setups will perform in real-world conditions.

**Acceptance Criteria:**
1. Implementation of Brompton-specific formulas (GR = CR / (RS * HGR) * 100%, DM = (AOD * 25.4 * π * GR) / 1000, SPD = (DM / 1000) * CAD * 60)
2. Support for all hub gear types (BSR, BWR, SA8) and their ratios
3. Cadence-based speed and distance calculations
4. Real-time calculation performance (<100ms response time)
5. Comprehensive unit tests for all calculation scenarios
6. Error handling for invalid configurations
7. Caching strategy for frequently used calculations

## Story 2.3: Model Browser & Search

**As a** user,  
**I want** to browse and search all Brompton models with their specifications,  
**so that** I can research and compare different bikes before making a purchase decision.

**Acceptance Criteria:**
1. Responsive grid layout for browsing Brompton models by line and type
2. Advanced search and filter capabilities (by line, type, components, price range)
3. Detailed model view with specifications and performance metrics
4. Side-by-side comparison of multiple models
5. Sort options by various criteria (price, weight, gear range, etc.)
6. Performance indicators for different riding scenarios
7. Favorite/bookmark functionality for interesting models

## Story 2.4: Configuration Builder Interface

**As a** user,  
**I want** an interactive interface to build and test custom Brompton configurations,  
**so that** I can experiment with different component combinations and see their performance impact.

**Acceptance Criteria:**
1. Interactive component selectors for chainrings, sprockets, hub gears, and tires
2. Real-time performance updates as configurations change
3. Visual compatibility indicators for component combinations
4. Save and load custom configurations
5. Performance comparison with stock configurations
6. Detailed breakdown of gear ratios and speed ranges
7. Export/share functionality for custom builds

## Story 2.5: Performance Visualization

**As a** user,  
**I want** clear visual representations of gear performance data,  
**so that** I can easily understand and compare different configurations at a glance.

**Acceptance Criteria:**
1. Gear ratio charts and graphs for different configurations
2. Speed range visualization across all gear combinations
3. Comparison views showing performance differences
4. Interactive charts with hover details and filtering
5. Mobile-optimized chart layouts and interactions
6. Export capabilities for performance data
7. Accessibility features for screen readers and colorblind users