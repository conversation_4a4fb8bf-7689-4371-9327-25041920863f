# Requirements

## Functional

**FR1: User Authentication System**
- Email-based user registration with verification code delivery
- Secure login/logout functionality with JWT token management
- User profile creation and management with basic information
- Password reset functionality via email verification

**FR2: Brompton Database Management**
- Complete database of 11 official Brompton models with specifications
- Chainring, rear sprocket, hub gear, and tire configuration data
- Model categorization by line (A, C, P, T, G) and type (Urban, Explore, etc.)
- Search and filter capabilities for Brompton models

**FR3: Interactive Gear Calculator**
- Real-time gear ratio calculations using Brompton-specific formulas
- Support for all configuration variables (chainrings, sprockets, hub gears, tires)
- Cadence-based speed and distance calculations
- Visual representation of gear combinations and performance metrics

**FR4: User Garage System**
- Create and manage multiple Brompton bike profiles
- Assign custom nicknames to garage bikes
- Add bikes from official Brompton database or custom configurations
- Performance comparison between different garage configurations

**FR5: Configuration Customization**
- Interactive controls for modifying chainrings, tires, hub gears
- Real-time performance updates as configurations change
- Save and load custom configurations
- Validation of compatible component combinations

**FR6: Mobile User Interface**
- Swipe gestures for bike/model navigation
- Card flip animations for detailed information display
- Touch-optimized controls and interactions
- Responsive design for various mobile screen sizes

**FR7: Social Sharing Features**
- Generate shareable images of garage configurations
- Include bike specifications and performance metrics in shares
- Direct sharing to social media platforms
- Save images to device for offline sharing

**FR8: Onboarding Flow**
- Guided setup for new users with Brompton line selection
- Interactive model choice with swipe navigation
- Progress tracking through onboarding steps
- Skip option for experienced users

## Non-Functional

**NFR1: Performance Requirements**
- <2 second initial app load time
- <500ms response time for all interactions
- 60fps animation performance for all transitions
- <100ms calculation time for gear ratios

**NFR2: Mobile Optimization**
- Progressive Web App (PWA) capabilities
- Offline functionality for core features
- Installable home screen icon
- Push notification support

**NFR3: Data Accuracy**
- 99.9% accuracy in gear ratio calculations
- Manufacturer specification compliance
- Regular data updates for new Brompton models
- Validation against real-world performance data

**NFR4: User Experience**
- Intuitive navigation requiring minimal learning curve
- Consistent design language throughout the app
- Accessibility compliance with WCAG 2.1 AA standards
- Multi-language support (English/Chinese initially)

**NFR5: Technical Infrastructure**
- React 18+ with TypeScript for type safety
- Chakra UI component library for responsive design
- Framer Motion for animations and gestures
- PostgreSQL database with Redis caching

**NFR6: Security & Privacy**
- GDPR compliance for user data handling
- Secure authentication with JWT tokens
- Encrypted data transmission (HTTPS)
- Regular security audits and updates