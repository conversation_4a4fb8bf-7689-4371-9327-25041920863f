# Goals and Background Context

## Goals
- **Deliver mobile-first Brompton performance calculator** with accurate gear ratio calculations and engaging UI
- **Create user garage system** for managing multiple Brompton configurations and performance comparisons
- **Implement social sharing features** to drive organic user growth and community engagement
- **Establish technical foundation** using React + Chakra UI with smooth animations and 60fps performance
- **Validate product-market fit** through MVP features that solve real Brompton owner pain points

## Background Context
Brompton owners and potential buyers face significant barriers understanding bicycle performance characteristics due to the technical complexity of gear ratios and multiple configuration variables. Current solutions are desktop-based spreadsheets or generic bike calculators that lack Brompton-specific data and mobile optimization. BromieWorks transforms these complex calculations into engaging mobile experiences with card flips, swipe gestures, and real-time visualizations, making bicycle customization accessible and enjoyable.

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | Initial PRD creation based on project brief | <PERSON> (PM) |