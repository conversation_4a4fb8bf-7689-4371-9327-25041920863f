# Epic List

**Epic 1: Foundation & Authentication** - Establish project infrastructure, user authentication, and core data models while delivering a functional landing page that demonstrates the app's value proposition.

**Epic 2: Brompton Database & Gear Calculator** - Build the complete Brompton model database and implement the core gear calculation engine that powers the app's primary value proposition.

**Epic 3: User Garage & Configuration System** - Create the user's personal bike management system with configuration customization and performance comparison capabilities.

**Epic 4: Mobile UI & Interactive Features** - Implement the mobile-first interface with swipe gestures, card flips, and engaging animations that differentiate the user experience.

**Epic 5: Social Sharing & Onboarding** - Add the social sharing features and guided onboarding flow to drive user acquisition and retention.