# Technical Assumptions

## Repository Structure: Monorepo
**Decision:** Single monorepo containing frontend, backend, and shared packages
**Rationale:** Monorepo simplifies dependency management between frontend and backend, ensures consistent tooling, and facilitates code sharing. Given the small team size (2-3 people max), the coordination overhead of multiple repositories would outweigh the benefits.

## Service Architecture: Serverless within Monorepo
**Decision:** Serverless functions within a monolithic structure using Next.js API routes
**Rationale:** Serverless architecture provides scalability without operational overhead, perfect for a small team. Next.js API routes offer seamless integration with the React frontend while keeping deployment simple and cost-effective within free-tier limits.

## Testing Requirements: Unit + Integration
**Decision:** Unit tests for core logic, integration tests for critical user flows, minimal E2E tests
**Rationale:** Given budget constraints and the calculation-intensive nature of the app, focus on unit tests for gear calculations and integration tests for user authentication/data flows. Limited E2E testing for critical paths only.

## Additional Technical Assumptions and Requests

**Frontend Technology Stack:**
- **React 18+ with TypeScript:** Type safety for complex calculation logic and better developer experience
- **Chakra UI:** Rapid development with accessible, responsive components and design system
- **Framer Motion:** High-performance animations for card flips and swipe gestures
- **React Query:** Efficient server state management for API calls and caching
- **Zustand:** Lightweight client state management for UI state

**Backend Technology Stack:**
- **Next.js API Routes:** Serverless functions integrated with frontend for seamless development
- **PostgreSQL:** Reliable relational database for user data and configurations
- **Redis:** High-performance caching for frequently accessed Brompton specification data
- **JWT Authentication:** Stateless authentication for scalability
- **AWS S3/Cloudinary:** Cost-effective file storage for user-generated images

**Data Architecture:**
- **Brompton Specifications:** Static data stored in database with admin interface for updates
- **User Configurations:** Relational data structure supporting multiple bikes per user
- **Calculation Engine:** Centralized service for gear ratio calculations with caching
- **Image Generation:** Server-side image creation for social sharing features

**Performance Considerations:**
- **Code Splitting:** Route-based and component-based splitting for optimal load times
- **Service Worker:** Offline caching for core Brompton data and user garage
- **CDN Delivery:** Static assets and Brompton images delivered via CDN
- **Database Optimization:** Indexed queries for common filtering and sorting operations

**Deployment & Infrastructure:**
- **Vercel:** Frontend and serverless function deployment with automatic scaling
- **Supabase/PlanetScale:** Managed PostgreSQL database with generous free tier
- **Upstash/Redis Labs:** Managed Redis caching for performance optimization
- **GitHub Actions:** CI/CD pipeline with automated testing and deployment

**Mobile Optimization:**
- **PWA Configuration:** Service worker, manifest file, and offline capabilities
- **Touch Event Handling:** Custom gesture recognizers for swipe interactions
- **Responsive Breakpoints:** Mobile-first design with tablet and desktop adaptations
- **Performance Budget:** <1MB initial bundle size, <50KB additional chunks

**Security Considerations:**
- **HTTPS:** All communications encrypted with SSL/TLS
- **Environment Variables:** Sensitive configuration stored securely
- **Input Validation:** Server-side validation for all user inputs
- **Rate Limiting:** API protection against abuse and DoS attacks