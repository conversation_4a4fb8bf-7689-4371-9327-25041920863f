# BromieWorks Frontend Architecture Document

## Introduction

This document defines the frontend architecture for BromieWorks, bridging the gap between the completed backend architecture and UI/UX specification. It provides the technical blueprint for implementing the mobile-first, interactive Brompton bike calculator with React 18+, TypeScript, and Chakra UI.

### Relationship to Backend Architecture

This frontend architecture document works in conjunction with the existing backend architecture document (`docs/architecture.md`). The technology stack decisions, data models, API specifications, and security requirements documented there are definitive for the entire project. This document focuses specifically on frontend implementation patterns, component architecture, and user interaction details.

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | Initial frontend architecture creation | Sally (UX Expert) |

## High Level Frontend Architecture

### Technical Summary

BromieWorks frontend is a React 18+ Progressive Web Application (PWA) built with TypeScript and Chakra UI, featuring server-side rendering capabilities through Next.js. The architecture implements a mobile-first design system with gesture-based navigation, real-time gear calculations, and social sharing capabilities. The frontend connects to the serverless backend via REST APIs and implements offline functionality through service workers and local storage strategies.

### High Level Overview

**Frontend Architecture Style:** Single Page Application (SPA) with PWA capabilities and optional SSR

**Key Architectural Decisions:**
- **React 18+ with TypeScript:** Type safety for complex calculation logic and better developer experience
- **Next.js Framework:** Unified framework supporting both frontend and serverless backend functions
- **Chakra UI Component Library:** Rapid development with accessible, responsive components
- **Framer Motion:** High-performance animations for card flips and swipe gestures
- **Zustand:** Lightweight client state management for UI state
- **React Query:** Efficient server state management for API calls and caching

### Frontend Architecture Diagram

```mermaid
graph TD
    A[User Device] --> B[React Application]
    B --> C[PWA Service Worker]
    B --> D[React Router]
    
    D --> E[Pages Layer]
    E --> F[Component Library]
    F --> G[UI Components]
    F --> H[Business Logic Components]
    
    H --> I[State Management]
    I --> J[Zustand Stores]
    I --> K[React Query Cache]
    
    B --> L[API Layer]
    L --> M[REST API Client]
    L --> N[Authentication Service]
    
    C --> O[Offline Storage]
    O --> P[IndexedDB]
    O --> Q[Local Storage]
    
    M --> R[Backend APIs]
    R --> S[Next.js Serverless Functions]
    S --> T[PostgreSQL Database]
    S --> U[Redis Cache]
```

### Frontend Architectural Patterns

- **Component-First Architecture:** UI built from reusable, composable components with clear separation of concerns
- **State Management分层:** Client state (Zustand) vs Server state (React Query) with clear boundaries
- **Mobile-First Responsive Design:** Progressive enhancement from mobile to desktop
- **Gesture-Based Interaction:** Custom gesture recognizers for swipe navigation and card flips
- **Progressive Web App:** Offline capabilities, installable, push notifications
- **Performance-Optimized:** Code splitting, lazy loading, and optimized bundle sizes

## Frontend Tech Stack

### Frontend Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|----------|
| **Language** | TypeScript | 5.3.3 | Type-safe frontend development | Strong typing for complex calculations, better IDE support, team expertise |
| **Framework** | Next.js | 14.0.0 | React framework with SSR/SSG | Unified frontend/backend, excellent performance, built-in optimizations |
| **UI Library** | Chakra UI | 2.8.0 | Component library and design system | Accessibility, responsive design, rapid development, theming |
| **Animation** | Framer Motion | 10.16.0 | Gesture animations and transitions | High-performance 60fps animations, gesture support, physics-based |
| **State Mgmt** | Zustand | 4.4.0 | Client state management | Lightweight, simple API, TypeScript support, good performance |
| **Data Fetching** | React Query | 5.0.0 | Server state and API caching | Automatic caching, background updates, optimistic updates |
| **Forms** | React Hook Form | 7.47.0 | Form management and validation | Performance, minimal re-renders, great with TypeScript |
| **Validation** | Zod | 3.22.0 | Schema validation | Type-safe validation, great with React Hook Form |
| **Testing** | Jest + React Testing Library | 29.7.0 | Unit and integration testing | Industry standard, good component testing, accessibility testing |
| **Build Tools** | Turbopack | 1.1.0 | Build system and bundler | Fast builds, great DX, Next.js integration |
| **PWA** | Next PWA | 0.19.0 | Progressive Web App features | Service worker generation, offline support, manifest generation |

### Development Tools

- **Package Manager:** pnpm 8.10.0 - Fast, disk space efficient, monorepo support
- **Linting:** ESLint 8.50.0 with TypeScript plugin - Code quality and consistency
- **Formatting:** Prettier 3.0.0 - Consistent code formatting
- **Git Hooks:** Husky 8.0.0 + lint-staged 14.0.0 - Pre-commit quality checks
- **Type Checking:** TypeScript 5.3.3 strict mode - Type safety
- **Bundle Analysis:** @next/bundle-analyzer - Performance optimization

## Frontend Data Models & Types

### Core Type Definitions

```typescript
// User types
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Brompton specification types
interface BromptonLine {
  id: string;
  name: string;
  description: string;
  models: BromptonModel[];
}

interface BromptonModel {
  id: string;
  name: string;
  lineId: string;
  type: 'urban' | 'explore' | 'sport' | 'utility';
  price: number;
  weight: number;
  defaultConfiguration: BikeConfiguration;
  imageUrl: string;
}

interface ComponentSpec {
  id: string;
  type: 'chainring' | 'sprocket' | 'hub-gear' | 'tire';
  name: string;
  teeth?: number;
  ratio?: number;
  size?: string;
  compatibleWith: string[];
}

// Bike configuration types
interface BikeConfiguration {
  id: string;
  modelId: string;
  chainringId: string;
  sprocketId: string;
  hubGearId: string;
  tireId: string;
  nickname?: string;
  isCustom: boolean;
}

// Performance calculation types
interface GearCalculation {
  gearRatio: number;
  development: number; // meters
  speedAtCadence: number; // km/h at 60 RPM
  gearInches: number;
  gearRange: {
    lowest: number;
    highest: number;
    percentage: number;
  };
}

// Garage types
interface GarageBike {
  id: string;
  userId: string;
  modelId: string;
  configuration: BikeConfiguration;
  nickname: string;
  notes?: string;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Social sharing types
interface ShareableImage {
  id: string;
  bikeId: string;
  template: string;
  customizations: ImageCustomization;
  generatedUrl: string;
  createdAt: Date;
}
```

### API Response Types

```typescript
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: ValidationError[];
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface ValidationError {
  field: string;
  message: string;
  code: string;
}
```

## Frontend Component Architecture

### Component Organization

```mermaid
graph TD
    A[Pages] --> B[Layout Components]
    A --> C[Feature Components]
    A --> D[UI Components]
    
    B --> E[AppLayout]
    B --> F[AuthLayout]
    B --> G[OnboardingLayout]
    
    C --> H[Garage Components]
    C --> I[Calculator Components]
    C --> J[Model Browser Components]
    C --> K[Social Components]
    
    H --> L[BikeCard]
    H --> M[GarageGrid]
    H --> N[ConfigurationEditor]
    
    I --> O[GearCalculator]
    I --> P[PerformanceChart]
    I --> Q[RealTimeDisplay]
    
    J --> R[ModelGrid]
    J --> S[ModelCard]
    J --> T[ComparisonView]
    
    K --> U[ShareModal]
    K --> V[ImageGenerator]
    K --> W[CommunityGrid]
    
    D --> X[Form Components]
    D --> Y[Navigation Components]
    D --> Z[Feedback Components]
```

### Key Component Specifications

#### BikeCard Component
**Purpose:** Display bike information with interactive capabilities
**Key Interfaces:**
```typescript
interface BikeCardProps {
  bike: GarageBike;
  onViewDetails: (bikeId: string) => void;
  onConfigure: (bikeId: string) => void;
  onShare: (bikeId: string) => void;
  isSelected?: boolean;
  variant: 'garage' | 'browser' | 'comparison';
}
```

#### GearCalculator Component
**Purpose:** Real-time gear ratio calculation interface
**Key Interfaces:**
```typescript
interface GearCalculatorProps {
  configuration: BikeConfiguration;
  onConfigurationChange: (config: BikeConfiguration) => void;
  availableComponents: ComponentSpec[];
  showAdvanced?: boolean;
}
```

#### SwipeCarousel Component
**Purpose:** Gesture-based navigation for bike/model browsing
**Key Interfaces:**
```typescript
interface SwipeCarouselProps {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  onSwipe?: (direction: 'left' | 'right') => void;
  onItemSelect?: (index: number) => void;
  loop?: boolean;
  sensitivity?: number;
}
```

#### PerformanceChart Component
**Purpose:** Visual representation of gear performance data
**Key Interfaces:**
```typescript
interface PerformanceChartProps {
  calculations: GearCalculation[];
  comparisonCalculations?: GearCalculation[][];
  chartType: 'line' | 'bar' | 'radar';
  interactive?: boolean;
  onExport?: (format: 'png' | 'svg' | 'data') => void;
}
```

### Component State Management Strategy

**Local Component State:** useState for UI state that doesn't need to be shared
**Global UI State:** Zustand stores for cross-component state (theme, navigation, user preferences)
**Server State:** React Query for API data, caching, and synchronization
**Form State:** React Hook Form for complex form state and validation

```typescript
// Example Zustand store
interface GarageState {
  bikes: GarageBike[];
  selectedBikeId: string | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchBikes: () => Promise<void>;
  addBike: (bike: Omit<GarageBike, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateBike: (id: string, updates: Partial<GarageBike>) => Promise<void>;
  deleteBike: (id: string) => Promise<void>;
  selectBike: (id: string) => void;
}
```

## External API Integrations

### Authentication Service

**Purpose:** User authentication and session management
**Integration:** JWT-based authentication with refresh tokens
**Key Endpoints:**
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/logout` - User logout

### Brompton Data API

**Purpose:** Access Brompton model specifications and component data
**Integration:** RESTful API with caching strategies
**Key Endpoints:**
- `GET /api/brompton/lines` - Get all Brompton lines
- `GET /api/brompton/models` - Get models with filtering
- `GET /api/brompton/components` - Get component specifications
- `GET /api/brompton/models/:id` - Get specific model details

### Gear Calculation API

**Purpose:** Perform gear ratio calculations and performance analysis
**Integration:** Real-time calculations with server-side validation
**Key Endpoints:**
- `POST /api/calculator/calculate` - Calculate gear ratios
- `GET /api/calculator/performance/:configId` - Get performance metrics
- `POST /api/calculator/compare` - Compare multiple configurations

### Social Sharing API

**Purpose:** Generate shareable images and manage social features
**Integration:** Image generation and social media platform integration
**Key Endpoints:**
- `POST /api/share/generate` - Generate shareable image
- `GET /api/share/templates` - Get available templates
- `POST /api/social/post` - Post to social media platforms

## Core Frontend Workflows

### User Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Service Worker
    participant A as Auth API
    participant C as Cache

    U->>F: Enter email
    F->>A: POST /api/auth/register
    A->>C: Store user data
    A->>F: Return JWT tokens
    F->>F: Store tokens securely
    F->>S: Register service worker
    S->>S: Cache user data offline
    F->>U: Redirect to onboarding
```

### Gear Calculation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant C as Calculator Component
    participant A as Calculation API
    participant R as React Query Cache

    U->>C: Change component selection
    C->>F: Update configuration state
    F->>A: POST /api/calculator/calculate
    A->>A: Calculate gear ratios
    A->>F: Return performance data
    F->>R: Cache calculation results
    F->>C: Update display with new data
    C->>U: Show real-time performance
```

### Social Sharing Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Share Modal
    participant I as Image Generator API
    participant P as Social Platform

    U->>F: Click share button
    F->>S: Open share modal
    S->>U: Show template options
    U->>S: Select template and customize
    S->>I: POST /api/share/generate
    I->>I: Generate image
    I->>S: Return image URL
    S->>F: Display preview
    U->>S: Select platform to share
    S->>P: Open native share dialog
    P->>U: Show sharing interface
```

### Offline Data Sync Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Service Worker
    participant O as Offline Storage
    participant A as API

    U->>F: Use app offline
    F->>S: Check connectivity
    S->>O: Load cached data
    O->>F: Return offline data
    F->>U: Show offline interface
    
    Note over U,A: Network reconnects
    S->>S: Detect network change
    S->>O: Get pending changes
    S->>A: Sync offline changes
    A->>S: Confirm sync
    S->>F: Update UI state
    F->>U: Show sync complete
```

## Progressive Web App Implementation

### PWA Configuration

**Manifest Configuration:**
```json
{
  "name": "BromieWorks",
  "short_name": "Bromie",
  "description": "Brompton bike calculator and garage manager",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#1a5f3f",
  "theme_color": "#1a5f3f",
  "orientation": "portrait-primary",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png", 
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### Service Worker Strategy

**Caching Strategy:**
- **Static Assets:** Cache-first with version-based invalidation
- **API Responses:** Network-first with fallback to cache
- **Brompton Data:** Stale-while-revalidate for frequently accessed data
- **User Data:** Network-only with offline queuing

```typescript
// Service worker caching strategy
const CACHE_STRATEGIES = {
  static: 'cache-first',
  api: 'network-first',
  data: 'stale-while-revalidate',
  user: 'network-only'
};
```

### Offline Functionality

**Offline Features:**
- **Core App Functionality:** Access to Brompton specifications and basic calculator
- **Garage Management:** View and edit bikes offline with sync on reconnect
- **Performance Calculations:** Basic gear calculations without network
- **Image Generation:** Queue image generation requests for online processing

## State Management Architecture

### Client State (Zustand)

**Global State Stores:**
- **Auth Store:** User authentication state and session management
- **Garage Store:** User's bike collection and management
- **UI Store:** Theme preferences, navigation state, UI controls
- **Calculation Store:** Active calculation state and temporary configurations

```typescript
// Auth store example
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}
```

### Server State (React Query)

**API Data Management:**
- **Brompton Data:** Model specifications and component data with caching
- **User Data:** Garage bikes and configurations with background sync
- **Calculations:** Performance data with optimistic updates
- **Social Data:** Community configurations and sharing data

```typescript
// React Query hooks
const useBromptonModels = (filters?: ModelFilters) => {
  return useQuery({
    queryKey: ['brompton-models', filters],
    queryFn: () => api.brompton.getModels(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
};
```

### Form State (React Hook Form)

**Form Management:**
- **Configuration Forms:** Bike component selection with validation
- **Authentication Forms:** Login, registration, and password reset
- **Profile Forms:** User settings and preferences
- **Share Forms:** Image customization and social media options

```typescript
// Configuration form example
const {
  control,
  handleSubmit,
  watch,
  formState: { errors, isDirty }
} = useForm<BikeConfiguration>({
  resolver: zodResolver(configurationSchema),
  defaultValues: initialConfiguration
});
```

## Routing and Navigation

### Route Structure

```typescript
// Route definitions
const routes = [
  {
    path: '/',
    component: HomePage,
    protected: false
  },
  {
    path: '/auth/login',
    component: LoginPage,
    protected: false
  },
  {
    path: '/auth/register',
    component: RegisterPage,
    protected: false
  },
  {
    path: '/onboarding',
    component: OnboardingPage,
    protected: true
  },
  {
    path: '/garage',
    component: GaragePage,
    protected: true
  },
  {
    path: '/models',
    component: ModelBrowserPage,
    protected: true
  },
  {
    path: '/models/:id',
    component: ModelDetailPage,
    protected: true
  },
  {
    path: '/calculator',
    component: CalculatorPage,
    protected: true
  },
  {
    path: '/profile',
    component: ProfilePage,
    protected: true
  },
  {
    path: '/settings',
    component: SettingsPage,
    protected: true
  }
];
```

### Navigation Components

**Bottom Navigation:** Primary navigation for mobile users
```typescript
const navigationItems = [
  { icon: BikeIcon, label: '车型', path: '/models' },
  { icon: GarageIcon, label: '车库', path: '/garage' },
  { icon: ProfileIcon, label: '我的', path: '/profile' }
];
```

**Breadcrumb Navigation:** Secondary navigation for complex flows
**Gesture Navigation:** Swipe-based navigation between bikes and models

## Performance Optimization

### Code Splitting Strategy

**Route-based Splitting:** Each page loads its own chunk
**Component-based Splitting:** Heavy components loaded on demand
**Library Splitting:** Third-party libraries in separate chunks

```typescript
// Dynamic import example
const ModelDetailPage = dynamic(
  () => import('@/pages/ModelDetailPage'),
  { 
    loading: () => <PageLoader />,
    ssr: false 
  }
);
```

### Bundle Optimization

**Bundle Budget:**
- **Initial Bundle:** <500KB gzipped
- **Additional Chunks:** <200KB each
- **Total App Size:** <2MB installed

**Optimization Techniques:**
- **Tree Shaking:** Remove unused code
- **Code Minification:** Reduce bundle size
- **Image Optimization:** Modern formats and lazy loading
- **Font Optimization:** Subset fonts and local hosting

### Runtime Performance

**Rendering Optimization:**
- **React.memo:** Prevent unnecessary re-renders
- **useMemo/useCallback:** Optimize expensive operations
- **Virtualization:** Handle large lists efficiently
- **Concurrent Features:** Leverage React 18 concurrent rendering

**Network Optimization:**
- **API Caching:** Intelligent caching with React Query
- **Image Optimization:** Responsive images and modern formats
- **Font Loading:** Optimize font loading strategies
- **Prefetching:** Predictive loading of likely resources

## Security Implementation

### Authentication Security

**Token Management:**
- **JWT Storage:** HttpOnly cookies for refresh tokens, memory for access tokens
- **Token Refresh:** Automatic token refresh before expiration
- **Session Management:** Secure session handling with timeout

```typescript
// Token storage strategy
const tokenStorage = {
  accessToken: 'memory', // Stored in memory
  refreshToken: 'cookie', // HttpOnly, secure, sameSite
  userData: 'localStorage' // Encrypted user data
};
```

### Data Security

**Input Validation:**
- **Client-side Validation:** Zod schemas for form validation
- **Server-side Validation:** Always validate on server regardless of client validation
- **Sanitization:** HTML escaping and XSS prevention

**API Security:**
- **HTTPS Enforcement:** All API calls over HTTPS
- **CORS Configuration:** Proper CORS setup for API calls
- **Rate Limiting:** Client-side rate limiting for API calls
- **Request Signing:** Optional request signing for sensitive operations

### Privacy Protection

**Data Handling:**
- **PII Protection:** Sensitive data handling and storage
- **GDPR Compliance:** User data rights and deletion
- **Local Storage:** Encrypted storage for sensitive data
- **Analytics:** Anonymous usage tracking with opt-out

## Testing Strategy

### Frontend Testing Philosophy

**Approach:** Testing pyramid with emphasis on unit and integration tests
**Coverage Goals:**
- **Unit Tests:** 80%+ coverage for business logic
- **Integration Tests:** 60%+ coverage for user flows
- **E2E Tests:** Critical user journeys only

### Test Types and Tools

**Unit Tests:**
- **Framework:** Jest 29.7.0
- **Runner:** React Testing Library
- **Focus:** Component logic, utilities, calculations
- **Mocking:** MSW for API mocking

**Integration Tests:**
- **Framework:** React Testing Library
- **Focus:** Component interactions, user flows
- **Tools:** MSW for API integration testing

**E2E Tests:**
- **Framework:** Playwright 1.40.0
- **Focus:** Critical user journeys
- **Environment:** CI/CD pipeline

**Performance Tests:**
- **Tools:** Lighthouse CI, WebPageTest
- **Metrics:** Core Web Vitals, bundle size, load times

### Test Organization

```
tests/
├── unit/
│   ├── components/
│   ├── utils/
│   └── hooks/
├── integration/
│   ├── flows/
│   └── api/
├── e2e/
│   ├── auth.spec.ts
│   ├── garage.spec.ts
│   └── calculator.spec.ts
└── performance/
    ├── lighthouse.spec.ts
    └── bundle.spec.ts
```

## Accessibility Implementation

### Accessibility Strategy

**Compliance Target:** WCAG 2.1 AA compliance
**Testing Approach:** Automated testing with manual validation
**Tools:** axe-core, WAVE, screen reader testing

### Key Accessibility Features

**Visual Accessibility:**
- **Color Contrast:** 4.5:1 for normal text, 3:1 for large text
- **Focus Management:** Visible focus indicators with 2px minimum
- **Text Scaling:** Responsive text with 200% zoom support
- **Motion Reduction:** Respect prefers-reduced-motion preference

**Interaction Accessibility:**
- **Keyboard Navigation:** Full keyboard access with logical tab order
- **Screen Reader Support:** Proper ARIA labels and live regions
- **Touch Targets:** Minimum 44x44px tap targets
- **Alternative Input:** Mouse, keyboard, touch, and switch device support

**Content Accessibility:**
- **Alternative Text:** Descriptive alt text for all images
- **Heading Structure:** Proper H1-H6 hierarchy
- **Form Labels:** Associated labels for all form inputs
- **Error Handling:** Clear error messages and recovery guidance

### Accessibility Testing

**Automated Testing:**
- **axe-core:** Automated accessibility testing
- **ESLint Plugin:** Accessibility linting rules
- **Jest Matchers:** Accessibility assertions

**Manual Testing:**
- **Screen Readers:** VoiceOver, NVDA, JAWS testing
- **Keyboard Navigation:** Tab order and focus management
- **Color Contrast:** Visual verification with tools
- **Mobile Testing:** Touch accessibility and zoom testing

## Internationalization Strategy

### Multi-language Support

**Supported Languages:**
- **Primary:** English (en)
- **Secondary:** Chinese (zh)
- **Architecture:** Extensible for additional languages

**Implementation:**
- **Library:** next-intl 3.0.0
- **Format:** ICU MessageFormat
- **File Structure:** Nested JSON files by language
- **Fallback:** English as default fallback

```typescript
// i18n configuration
const locales = ['en', 'zh'] as const;
const defaultLocale = 'en' as const;

interface Messages {
  en: typeof enMessages;
  zh: typeof zhMessages;
}
```

### Localization Strategy

**Date/Time Formatting:** Locale-specific date and time formats
**Number Formatting:** Locale-specific number and currency formats
**Text Direction:** Support for LTR and RTL languages
**Content Adaptation:** Culturally appropriate content and imagery

## Error Handling Strategy

### Frontend Error Handling

**Error Boundaries:** React error boundaries for component errors
**Global Error Handler:** Window error event listener
**API Error Handling:** Centralized API error processing
**User-Friendly Errors:** Clear error messages for users

```typescript
// Error boundary component
class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    logError(error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }
    return this.props.children;
  }
}
```

### Error Recovery

**Retry Mechanisms:** Automatic retry for failed API calls
**Offline Mode:** Graceful degradation when offline
**User Guidance:** Clear instructions for error recovery
**Error Reporting:** Optional error reporting to support team

## Monitoring and Analytics

### Performance Monitoring

**Core Web Vitals:** LCP, FID, CLS tracking
**User Timing:** Custom performance metrics
**Error Tracking:** Error boundary and API error monitoring
**Resource Monitoring:** Asset loading performance

**Tools:**
- **Analytics:** Google Analytics 4
- **Performance:** Web Vitals library
- **Error Monitoring:** Sentry or similar
- **User Behavior:** Hotjar or similar (with consent)

### User Analytics

**Event Tracking:** Key user interactions and flows
**Funnel Analysis:** Conversion tracking for key actions
**User Segmentation:** Behavior analysis by user type
**A/B Testing:** Framework for feature experimentation

## Development Workflow

### Local Development

**Development Environment:**
- **Package Manager:** pnpm for fast installs
- **Dev Server:** Next.js dev server with HMR
- **Type Checking:** TypeScript strict mode
- **Linting:** ESLint with pre-commit hooks

**Development Commands:**
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm test         # Run tests
pnpm lint         # Run linting
pnpm type-check   # Type checking
```

### Code Quality

**Linting Rules:** Custom ESLint configuration for React and TypeScript
**Formatting:** Prettier with consistent formatting rules
**Type Safety:** TypeScript strict mode with additional rules
**Testing:** Pre-commit test running for changed files

**Git Hooks:**
- **Pre-commit:** Linting, formatting, and tests
- **Pre-push:** Full test suite and type checking
- **Commit Msg:** Conventional commit messages

## Deployment Strategy

### Build Process

**Build Steps:**
1. **Type Checking:** Verify TypeScript types
2. **Linting:** Code quality checks
3. **Testing:** Run test suite
4. **Building:** Create optimized bundles
5. **Asset Optimization:** Optimize images and fonts
6. **PWA Generation:** Generate service worker and manifest

### Environment Configuration

**Environment Variables:**
- **Development:** Local development configuration
- **Staging:** Testing environment configuration
- **Production:** Production-optimized configuration

**Build Targets:**
- **Web:** Standard web build
- **PWA:** Progressive Web App build
- **Static:** Static site generation where possible

### Deployment Pipeline

**CI/CD Pipeline:**
1. **Trigger:** Git push to main branch
2. **Build:** Run full build process
3. **Test:** Execute all tests
4. **Deploy:** Deploy to Vercel
5. **Monitor:** Post-deployment health checks

**Deployment Environments:**
- **Production:** Main production environment
- **Staging:** Testing and validation environment
- **Preview:** Feature branch previews

## Source Tree Structure

### Frontend Source Organization

```
frontend/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   ├── page.tsx            # Home page
│   │   ├── auth/               # Auth pages
│   │   ├── garage/             # Garage pages
│   │   ├── models/             # Model browser pages
│   │   ├── calculator/         # Calculator pages
│   │   └── profile/            # Profile pages
│   ├── components/             # Reusable components
│   │   ├── ui/                 # Basic UI components
│   │   ├── forms/              # Form components
│   │   ├── layout/             # Layout components
│   │   ├── features/           # Feature-specific components
│   │   │   ├── garage/         # Garage components
│   │   │   ├── calculator/     # Calculator components
│   │   │   ├── models/         # Model browser components
│   │   │   └── social/         # Social sharing components
│   │   └── common/             # Common shared components
│   ├── hooks/                  # Custom React hooks
│   │   ├── useAuth.ts          # Authentication hook
│   │   ├── useGarage.ts        # Garage management hook
│   │   ├── useCalculator.ts    # Calculator hook
│   │   └── useOffline.ts       # Offline functionality hook
│   ├── stores/                 # Zustand stores
│   │   ├── authStore.ts        # Authentication store
│   │   ├── garageStore.ts      # Garage management store
│   │   ├── uiStore.ts          # UI state store
│   │   └── calculationStore.ts # Calculation state store
│   ├── services/               # API services
│   │   ├── apiClient.ts        # API client configuration
│   │   ├── authService.ts      # Authentication service
│   │   ├── bromptonService.ts  # Brompton data service
│   │   └── calculationService.ts # Calculation service
│   ├── utils/                  # Utility functions
│   │   ├── calculations.ts     # Gear calculation utilities
│   │   ├── validation.ts       # Validation utilities
│   │   ├── formatting.ts       # Data formatting utilities
│   │   └── storage.ts          # Storage utilities
│   ├── types/                  # TypeScript type definitions
│   │   ├── api.ts              # API response types
│   │   ├── brompton.ts         # Brompton data types
│   │   ├── garage.ts           # Garage types
│   │   └── common.ts           # Common shared types
│   ├── constants/              # Application constants
│   │   ├── routes.ts           # Route definitions
│   │   ├── endpoints.ts        # API endpoints
│   │   └── config.ts           # Configuration constants
│   ├── styles/                 # Style files
│   │   ├── theme.ts            # Chakra UI theme configuration
│   │   ├── components.css      # Component-specific styles
│   │   └── animations.css      # Animation styles
│   └── lib/                    # External library configurations
│       ├── chakra.ts           # Chakra UI configuration
│       ├── framer.ts           # Framer Motion configuration
│       ├── query.ts            # React Query configuration
│       └── i18n.ts             # Internationalization configuration
├── public/                     # Static assets
│   ├── icons/                  # App icons and PWA assets
│   ├── images/                 # Images and media
│   ├── fonts/                  # Font files
│   └── favicons/               # Favicon files
├── tests/                      # Test files
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   ├── e2e/                    # End-to-end tests
│   └── fixtures/               # Test fixtures and mocks
├── docs/                       # Documentation
│   ├── components/             # Component documentation
│   ├── api/                    # API documentation
│   └── deployment/             # Deployment documentation
├── scripts/                    # Build and deployment scripts
│   ├── build.js                # Build scripts
│   ├── deploy.js               # Deployment scripts
│   └── migrate.js              # Migration scripts
├── config/                     # Configuration files
│   ├── next.config.js          # Next.js configuration
│   ├── tailwind.config.js      # Tailwind CSS configuration
│   ├── tsconfig.json           # TypeScript configuration
│   ├── jest.config.js          # Jest configuration
│   └── eslint.config.js       # ESLint configuration
└── package.json                # Package dependencies and scripts
```

## Frontend Architecture Checklist

### Core Architecture Elements
- [ ] React 18+ with TypeScript configuration
- [ ] Next.js framework setup with app router
- [ ] Chakra UI component library integration
- [ ] Framer Motion animation library setup
- [ ] Zustand state management configuration
- [ ] React Query for server state management
- [ ] Progressive Web App configuration
- [ ] Service worker implementation
- [ ] Responsive design system
- [ ] Accessibility compliance framework

### Component Architecture
- [ ] Component library structure defined
- [ ] Key component interfaces specified
- [ ] State management strategy documented
- [ ] Component testing strategy
- [ ] Component documentation plan
- [ ] Performance optimization patterns
- [ ] Error handling in components
- [ ] Accessibility features in components

### Data and API Integration
- [ ] API client configuration
- [ ] Data type definitions
- [ ] Caching strategy implemented
- [ ] Offline data synchronization
- [ ] Error handling for API calls
- [ ] Authentication integration
- [ ] Real-time data updates
- [ ] Data validation and sanitization

### Performance and Optimization
- [ ] Code splitting strategy
- [ ] Bundle optimization plan
- [ ] Performance monitoring setup
- [ ] Core Web Vitals tracking
- [ ] Image optimization strategy
- [ ] Font loading optimization
- [ ] Runtime performance patterns
- [ ] Memory usage optimization

### Security and Privacy
- [ ] Authentication security implementation
- [ ] Data protection measures
- [ ] Input validation framework
- [ ] XSS prevention measures
- [ ] CSRF protection
- [ ] Privacy compliance features
- [ ] Secure token management
- [ ] Audit logging capabilities

### Testing Strategy
- [ ] Unit testing framework setup
- [ ] Integration testing approach
- [ ] E2E testing strategy
- [ ] Performance testing plan
- [ ] Accessibility testing
- [ ] Test coverage goals
- [ ] Mocking and fixture strategy
- [ ] CI/CD test integration

### Development Workflow
- [ ] Development environment setup
- [ ] Code quality tools configured
- [ ] Git hooks and pre-commit checks
- [ ] Build and deployment pipeline
- [ ] Documentation standards
- [ ] Code review process
- [ ] Team collaboration tools
- [ ] Onboarding documentation

## Next Steps

### Immediate Actions for Development Team

1. **Setup Development Environment**
   - Initialize Next.js project with TypeScript
   - Configure Chakra UI and Framer Motion
   - Setup Zustand and React Query
   - Configure ESLint and Prettier

2. **Implement Core Components**
   - Create layout components (AppLayout, Navigation)
   - Build basic UI components (Button, Input, Card)
   - Implement feature components (BikeCard, Calculator)
   - Setup gesture navigation components

3. **Setup State Management**
   - Configure Zustand stores for auth, garage, UI
   - Setup React Query for API caching
   - Implement form state management
   - Create offline data synchronization

4. **Implement Authentication Flow**
   - Create login/register pages
   - Implement authentication service
   - Setup protected routes
   - Configure session management

5. **Build Core Features**
   - Implement garage management system
   - Create gear calculator interface
   - Build model browser functionality
   - Add social sharing features

### Handoff to Development Team

**Architecture Document Review:**
- Schedule review meeting with development team
- Address any questions or concerns
- Ensure understanding of architectural decisions
- Confirm technology stack and tooling choices

**Development Setup:**
- Provide development environment setup guide
- Share configuration files and templates
- Document coding standards and best practices
- Setup project repository and CI/CD pipeline

**First Sprint Planning:**
- Break down epics into manageable stories
- Prioritize core functionality for MVP
- Estimate development effort
- Create sprint backlog and timeline

### Success Metrics

**Technical Metrics:**
- Lighthouse score >90 for all categories
- Core Web Vitals within good thresholds
- Bundle size under 2MB total
- Test coverage >80% for critical paths

**User Experience Metrics:**
- Time-to-first-interaction <3 seconds
- Task completion rate >90%
- User satisfaction score >4.0/5.0
- Mobile conversion rate > industry average

**Business Metrics:**
- User acquisition cost within target
- User retention rate >60%
- Feature adoption rate >70%
- Social sharing conversion >10%

This frontend architecture provides a comprehensive technical foundation for implementing the BromieWorks application. It balances technical excellence with practical implementation considerations, ensuring the development team can deliver a high-quality, performant, and accessible mobile-first Brompton bike calculator.