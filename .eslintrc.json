{"root": true, "env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended", "next/core-web-vitals", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "settings": {"react": {"version": "detect"}}, "overrides": [{"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser", "extends": ["@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-interface": "warn"}}, {"files": ["*.js", "*.jsx"], "rules": {"@typescript-eslint/no-var-requires": "off"}}]}