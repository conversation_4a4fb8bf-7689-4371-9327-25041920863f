{"name": "bromieworks", "version": "1.0.0", "description": "Brompton bike calculator and garage management application", "private": true, "workspaces": ["packages/*", "scripts/*"], "scripts": {"dev": "pnpm --filter web dev", "build": "pnpm --filter web build", "test": "pnpm -r test", "test:unit": "pnpm -r run test:unit", "test:integration": "pnpm -r run test:integration", "test:e2e": "pnpm -r run test:e2e", "lint": "pnpm -r lint", "lint:fix": "pnpm -r lint:fix", "format": "pnpm -r format", "format:check": "pnpm -r format:check", "typecheck": "pnpm -r typecheck", "clean": "pnpm -r clean", "install:all": "pnpm install && pnpm -r install", "upgrade": "pnpm upgrade --latest", "prepare": "husky install"}, "devDependencies": {"@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.31", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.2.4", "typescript": "^5.3.3"}, "engines": {"node": ">=20.11.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "repository": {"type": "git", "url": "https://github.com/your-org/bromieworks.git"}, "author": "BromieWorks Team", "license": "MIT", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}