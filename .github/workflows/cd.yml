name: CD

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      url: https://bromieworks.vercel.app
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.0
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.11.0
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install:all
        
      - name: Run tests
        run: pnpm test
        
      - name: Build application
        run: pnpm build
        
      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./packages/web

  deploy-preview:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    environment:
      name: preview
      url: ${{ steps.deploy.outputs.preview-url }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.0
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.11.0
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install:all
        
      - name: Run linter and type check
        run: |
          pnpm lint
          pnpm typecheck
          
      - name: Build application
        run: pnpm build
        
      - name: Deploy Preview to Vercel
        id: deploy
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./packages/web
          
      - name: Comment PR with deployment URL
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🚀 Preview deployment ready: ${{ steps.deploy.outputs.preview-url }}'
            })