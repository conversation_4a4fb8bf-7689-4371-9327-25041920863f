name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [20.11.0]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.0
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install:all
        
      - name: Run linter
        run: pnpm lint
        
      - name: Run type checking
        run: pnpm typecheck
        
      - name: Run unit tests
        run: pnpm test:unit
        
      - name: Run integration tests
        if: github.event_name == 'push'
        run: pnpm test:integration
        
      - name: Upload coverage reports
        if: github.event_name == 'push'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'